package com.ainative.mountainsurvival.security

import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty
import kotlin.random.Random

/**
 * 安全数值属性委托，用于防止内存修改攻击
 * 
 * 通过数值混淆和校验和机制来保护关键游戏数据，防止像GG修改器这样的工具进行作弊
 * 
 * @param initialValue 初始值
 * @param onTamperDetected 检测到篡改时的回调函数
 */
class SecureIntDelegate(
    initialValue: Int,
    private val onTamperDetected: () -> Unit
) : ReadWriteProperty<Any?, Int> {
    
    // 用于混淆的固定密钥
    private val encryptionKey: Int = generateStaticKey()
    
    // 用于校验和计算的盐值
    private val checksumSalt: Int = generateStaticSalt()
    
    // 存储混淆后的值
    private var obfuscatedValue: Int
    
    // 存储校验和
    private var checksum: Int
    
    init {
        // 初始化时设置混淆值和校验和
        obfuscatedValue = initialValue xor encryptionKey
        checksum = calculateChecksum(initialValue)
    }
    
    override fun getValue(thisRef: Any?, property: KProperty<*>): Int {
        // 从混淆值恢复原始值
        val recoveredValue = obfuscatedValue xor encryptionKey
        
        // 重新计算校验和进行验证
        val expectedChecksum = calculateChecksum(recoveredValue)
        
        // 检测篡改
        if (checksum != expectedChecksum) {
            // 发现篡改，调用回调函数
            onTamperDetected()
            // 返回一个安全的默认值或抛出异常
            throw SecurityException("检测到内存篡改！属性: ${property.name}")
        }
        
        return recoveredValue
    }
    
    override fun setValue(thisRef: Any?, property: KProperty<*>, value: Int) {
        // 更新混淆值
        obfuscatedValue = value xor encryptionKey
        
        // 更新校验和
        checksum = calculateChecksum(value)
    }
    
    /**
     * 计算校验和
     */
    private fun calculateChecksum(value: Int): Int {
        return value.hashCode() * 31 + checksumSalt
    }
    
    /**
     * 生成静态加密密钥
     */
    private fun generateStaticKey(): Int {
        // 使用固定种子确保每次运行时密钥相同，但对外部攻击者来说是不可预测的
        return 0x5A5A5A5A xor System.currentTimeMillis().toInt()
    }
    
    /**
     * 生成静态盐值
     */
    private fun generateStaticSalt(): Int {
        return 0x3C3C3C3C xor hashCode()
    }
}

/**
 * 安全浮点数属性委托
 */
class SecureFloatDelegate(
    initialValue: Float,
    private val onTamperDetected: () -> Unit
) : ReadWriteProperty<Any?, Float> {
    
    private val encryptionKey: Int = generateStaticKey()
    private val checksumSalt: Int = generateStaticSalt()
    
    // 将Float转换为Int进行存储以便进行位运算
    private var obfuscatedValue: Int
    private var checksum: Int
    
    init {
        obfuscatedValue = initialValue.toBits() xor encryptionKey
        checksum = calculateChecksum(initialValue)
    }
    
    override fun getValue(thisRef: Any?, property: KProperty<*>): Float {
        val recoveredBits = obfuscatedValue xor encryptionKey
        val recoveredValue = Float.fromBits(recoveredBits)
        
        val expectedChecksum = calculateChecksum(recoveredValue)
        
        if (checksum != expectedChecksum) {
            onTamperDetected()
            throw SecurityException("检测到内存篡改！属性: ${property.name}")
        }
        
        return recoveredValue
    }
    
    override fun setValue(thisRef: Any?, property: KProperty<*>, value: Float) {
        obfuscatedValue = value.toBits() xor encryptionKey
        checksum = calculateChecksum(value)
    }
    
    private fun calculateChecksum(value: Float): Int {
        return value.toBits().hashCode() * 31 + checksumSalt
    }
    
    private fun generateStaticKey(): Int {
        return 0x7B7B7B7B xor System.currentTimeMillis().toInt()
    }
    
    private fun generateStaticSalt(): Int {
        return 0x4D4D4D4D xor hashCode()
    }
}

/**
 * 便捷的工厂函数，用于创建安全的Int属性委托
 */
fun secureInt(initialValue: Int, onTamperDetected: () -> Unit): SecureIntDelegate {
    return SecureIntDelegate(initialValue, onTamperDetected)
}

/**
 * 便捷的工厂函数，用于创建安全的Float属性委托
 */
fun secureFloat(initialValue: Float, onTamperDetected: () -> Unit): SecureFloatDelegate {
    return SecureFloatDelegate(initialValue, onTamperDetected)
}
