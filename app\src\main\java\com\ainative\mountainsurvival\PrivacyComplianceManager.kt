package com.ainative.mountainsurvival

import android.content.Context
import android.util.Log
import com.kc.openset.config.controller.OSETCustomController

/**
 * 隐私合规管理器
 * 根据用户是否同意隐私政策来控制广告SDK的权限获取
 */
object PrivacyComplianceManager {
    
    private const val TAG = "PrivacyComplianceManager"
    
    /**
     * 检查用户是否已同意隐私政策
     */
    fun isPrivacyPolicyAgreed(context: Context): Boolean {
        val sharedPreferences = context.getSharedPreferences("privacy_policy", Context.MODE_PRIVATE)
        val agreed = sharedPreferences.getBoolean("agreed", false)
        val agreeTime = sharedPreferences.getLong("agree_time", 0)

        Log.d(TAG, "隐私政策同意状态: $agreed")
        PrivacyAuditLogger.logPrivacyPolicyStatusChange(agreed, agreeTime)

        return agreed
    }
    
    /**
     * 创建隐私合规控制器
     * 根据用户是否同意隐私政策来决定是否允许获取各种信息
     */
    fun createCustomController(context: Context): OSETCustomController {
        val isAgreed = isPrivacyPolicyAgreed(context)
        Log.d(TAG, "创建隐私合规控制器，用户同意状态: $isAgreed")
        
        return object : OSETCustomController() {
            /**
             * 是否允许SDK主动使用地理位置信息
             * 由于我们移除了位置权限，始终返回false
             */
            override fun canReadLocation(): Boolean {
                Log.d(TAG, "canReadLocation: false (权限已移除)")
                PrivacyAuditLogger.logDeniedInfoAccess("位置信息", "权限已从清单文件中移除")
                return false
            }

            /**
             * 当canReadLocation=false时，可传入地理位置信息
             * 我们不提供位置信息
             */
            override fun getLocation(): android.location.Location? {
                Log.d(TAG, "getLocation: null")
                return null
            }

            /**
             * 是否允许SDK主动使用手机硬件参数，如：imei, android_id, meid, imsi, iccid
             * 由于我们移除了READ_PHONE_STATE权限，始终返回false
             */
            override fun canUsePhoneState(): Boolean {
                Log.d(TAG, "canUsePhoneState: false (权限已移除)")
                PrivacyAuditLogger.logDeniedInfoAccess("电话状态信息", "READ_PHONE_STATE权限已从清单文件中移除")
                return false
            }

            /**
             * 当canUsePhoneState=false时，可传入IMEI信息
             * 我们不提供IMEI信息
             */
            override fun getImei(): String {
                Log.d(TAG, "getImei: empty")
                return ""
            }

            /**
             * 当canUsePhoneState=false时，可传入Android ID信息
             * 我们不提供Android ID信息
             */
            override fun getAndroidId(): String {
                Log.d(TAG, "getAndroidId: empty")
                return ""
            }

            /**
             * 是否允许SDK主动使用oaid
             * 只有用户同意隐私政策后才允许
             */
            override fun canUseOaid(): Boolean {
                Log.d(TAG, "canUseOaid: $isAgreed")
                return isAgreed
            }

            /**
             * 当canUseOaid=false时，可传入OAID信息
             * 我们不主动提供OAID信息
             */
            override fun getOaid(): String {
                Log.d(TAG, "getOaid: empty")
                return ""
            }

            /**
             * 是否允许SDK主动使用mac_address
             * 只有用户同意隐私政策后才允许，但实际上现代Android已限制MAC地址获取
             */
            override fun canUseMacAddress(): Boolean {
                Log.d(TAG, "canUseMacAddress: $isAgreed")
                return isAgreed
            }

            /**
             * 当canUseMacAddress=false时，可传入MAC地址信息
             * 我们不提供MAC地址信息
             */
            override fun getMacAddress(): String {
                Log.d(TAG, "getMacAddress: empty")
                return ""
            }

            /**
             * 是否允许SDK主动使用ACCESS_NETWORK_STATE权限
             * 网络状态是基本功能需要，但只有用户同意后才允许
             */
            override fun canUseNetworkState(): Boolean {
                Log.d(TAG, "canUseNetworkState: $isAgreed")
                return isAgreed
            }

            /**
             * 是否允许SDK主动使用存储权限
             * 只有用户同意隐私政策后才允许
             */
            override fun canUseStoragePermission(): Boolean {
                Log.d(TAG, "canUseStoragePermission: $isAgreed")
                return isAgreed
            }

            /**
             * 是否允许SDK主动读取app安装列表
             * 只有用户同意隐私政策后才允许
             */
            override fun canReadInstalledPackages(): Boolean {
                Log.d(TAG, "canReadInstalledPackages: $isAgreed")
                return isAgreed
            }

            /**
             * 当canReadInstalledPackages=false时，可传入应用列表信息
             * 我们不提供应用列表信息
             */
            override fun getInstalledPackages(): List<String>? {
                Log.d(TAG, "getInstalledPackages: null")
                return null
            }

            /**
             * 是否启用个性化广告开关
             * 只有用户同意隐私政策后才允许
             */
            override fun canUsePersonalizedAd(): Boolean {
                Log.d(TAG, "canUsePersonalizedAd: $isAgreed")
                return isAgreed
            }
        }
    }
}
