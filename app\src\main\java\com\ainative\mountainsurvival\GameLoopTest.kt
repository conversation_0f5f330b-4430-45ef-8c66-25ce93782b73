package com.ainative.mountainsurvival

import android.util.Log

/**
 * 游戏循环测试工具
 * 验证完整的游戏循环逻辑是否正常工作
 */
object GameLoopTest {
    
    private const val TAG = "GameLoopTest"
    
    /**
     * 测试完整的游戏循环
     * 模拟玩家进行多天的游戏
     */
    fun testGameLoop(): GameLoopTestResult {
        Log.d(TAG, "开始游戏循环测试...")
        
        val startTime = System.currentTimeMillis()
        val issues = mutableListOf<String>()
        val dayResults = mutableListOf<DayTestResult>()
        
        try {
            // 初始化游戏
            GameManager.initializeGame()
            val initialState = GameManager.gameState.copy()
            
            Log.d(TAG, "初始状态: $initialState")
            
            // 模拟7天的游戏循环
            for (day in 1..7) {
                Log.d(TAG, "测试第 $day 天...")
                
                val dayStartState = GameManager.gameState.copy()
                
                // 模拟玩家选择（消耗一些体力，获得一些木柴）
                val testChoice = Choice(
                    text = "测试选择 - 第${day}天",
                    effects = mapOf(
                        "stamina" to -10,
                        "firewood" to 8
                    )
                )
                
                val choiceResult = GameManager.applyChoice(testChoice)
                
                if (!choiceResult.success) {
                    issues.add("第${day}天选择应用失败: ${choiceResult.message}")
                    break
                }
                
                val afterChoiceState = GameManager.gameState.copy()
                
                // 执行夜晚阶段
                val nightResult = GameManager.performNightPhase()
                
                val afterNightState = GameManager.gameState.copy()
                
                // 检查游戏是否结束
                val isGameOver = GameManager.checkGameOver()
                val gameOverReason = if (isGameOver) GameManager.getGameOverReason() else null
                
                dayResults.add(
                    DayTestResult(
                        day = day,
                        dayStartState = dayStartState,
                        afterChoiceState = afterChoiceState,
                        afterNightState = afterNightState,
                        nightResult = nightResult,
                        isGameOver = isGameOver,
                        gameOverReason = gameOverReason
                    )
                )
                
                Log.d(TAG, "第${day}天完成: 体温=${afterNightState.warmth}, 木柴=${afterNightState.firewood}, 游戏结束=$isGameOver")
                
                // 如果游戏结束，停止测试
                if (isGameOver) {
                    Log.d(TAG, "游戏在第${day}天结束: $gameOverReason")
                    break
                }
            }
            
            val totalTime = System.currentTimeMillis() - startTime
            
            return GameLoopTestResult(
                success = issues.isEmpty(),
                totalTimeMs = totalTime,
                issues = issues,
                initialState = initialState,
                dayResults = dayResults,
                finalState = GameManager.gameState.copy()
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "游戏循环测试异常", e)
            issues.add("游戏循环测试异常: ${e.message}")
            
            return GameLoopTestResult(
                success = false,
                totalTimeMs = System.currentTimeMillis() - startTime,
                issues = issues,
                initialState = GameManager.gameState.copy(),
                dayResults = dayResults,
                finalState = GameManager.gameState.copy(),
                exception = e
            )
        }
    }
    
    /**
     * 测试游戏结束条件
     */
    fun testGameOverConditions(): GameOverTestResult {
        Log.d(TAG, "测试游戏结束条件...")
        
        val issues = mutableListOf<String>()
        
        try {
            // 测试体温过低的游戏结束
            GameManager.initializeGame()
            
            // 将体温设为0
            val freezeChoice = Choice(
                text = "冻死测试",
                effects = mapOf("warmth" to -GameManager.gameState.warmth)
            )
            GameManager.applyChoice(freezeChoice)
            
            if (!GameManager.checkGameOver()) {
                issues.add("体温为0时游戏未结束")
            }
            
            if (GameManager.getGameOverReason() != "体温过低") {
                issues.add("体温过低时游戏结束原因不正确")
            }
            
            // 测试存活7天的胜利条件
            GameManager.initializeGame()
            
            // 手动设置为第8天
            repeat(7) {
                GameManager.performNightPhase()
            }
            
            if (!GameManager.checkGameOver()) {
                issues.add("第8天时游戏未结束")
            }
            
            if (!GameManager.isVictory()) {
                issues.add("存活7天时未判定为胜利")
            }
            
            return GameOverTestResult(
                success = issues.isEmpty(),
                issues = issues
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "游戏结束条件测试异常", e)
            issues.add("游戏结束条件测试异常: ${e.message}")
            
            return GameOverTestResult(
                success = false,
                issues = issues,
                exception = e
            )
        }
    }
    
    /**
     * 游戏循环测试结果
     */
    data class GameLoopTestResult(
        val success: Boolean,
        val totalTimeMs: Long,
        val issues: List<String>,
        val initialState: GameState,
        val dayResults: List<DayTestResult>,
        val finalState: GameState,
        val exception: Exception? = null
    )
    
    /**
     * 每天测试结果
     */
    data class DayTestResult(
        val day: Int,
        val dayStartState: GameState,
        val afterChoiceState: GameState,
        val afterNightState: GameState,
        val nightResult: GameManager.NightPhaseResult,
        val isGameOver: Boolean,
        val gameOverReason: String?
    )
    
    /**
     * 游戏结束条件测试结果
     */
    data class GameOverTestResult(
        val success: Boolean,
        val issues: List<String>,
        val exception: Exception? = null
    )
}
