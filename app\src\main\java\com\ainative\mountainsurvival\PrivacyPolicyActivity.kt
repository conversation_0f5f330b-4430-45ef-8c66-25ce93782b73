package com.ainative.mountainsurvival

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.support.v7.app.AppCompatActivity
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import com.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding

/**
 * 隐私政策弹窗Activity
 * 在app启动前显示隐私政策，用户必须同意后才能进入app
 */
class PrivacyPolicyActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "PrivacyPolicyActivity"
        private const val PRIVACY_POLICY_URL = "https://vwk4xgjlcg1.feishu.cn/docx/WRqEdaUEJo7pn3xNuZicsa1lnhh"
    }

    private lateinit var binding: ActivityPrivacyPolicyBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "创建隐私政策界面")

        // 初始化视图绑定
        binding = ActivityPrivacyPolicyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 隐藏ActionBar
        supportActionBar?.hide()

        // 设置隐私政策内容的超链接
        setupPrivacyPolicyContent()

        // 设置按钮点击监听器
        setupClickListeners()

        Log.d(TAG, "隐私政策界面初始化完成")
    }

    /**
     * 设置隐私政策内容，包含超链接
     */
    private fun setupPrivacyPolicyContent() {
        val content = getString(R.string.privacy_policy_content)
        val linkText = getString(R.string.privacy_policy_link_text)
        
        // 创建SpannableString来添加超链接
        val spannableString = SpannableString(content)
        
        // 查找所有《雪山求生隐私政策》文本的位置
        var startIndex = 0
        while (true) {
            val index = content.indexOf(linkText, startIndex)
            if (index == -1) break
            
            // 创建点击事件
            val clickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    openPrivacyPolicyUrl()
                }
            }
            
            // 设置超链接样式和点击事件
            spannableString.setSpan(
                clickableSpan,
                index,
                index + linkText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            
            // 设置超链接颜色
            spannableString.setSpan(
                ForegroundColorSpan(resources.getColor(android.R.color.holo_blue_dark)),
                index,
                index + linkText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            
            startIndex = index + linkText.length
        }
        
        // 设置文本和启用链接点击
        binding.privacyContentTextView.text = spannableString
        binding.privacyContentTextView.movementMethod = LinkMovementMethod.getInstance()
    }

    /**
     * 打开隐私政策网页
     */
    private fun openPrivacyPolicyUrl() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(PRIVACY_POLICY_URL))
            startActivity(intent)
            Log.d(TAG, "打开隐私政策网页: $PRIVACY_POLICY_URL")
        } catch (e: Exception) {
            Log.e(TAG, "无法打开隐私政策网页", e)
        }
    }

    /**
     * 设置按钮点击监听器
     */
    private fun setupClickListeners() {
        // 同意按钮
        binding.agreeButton.setOnClickListener {
            Log.d(TAG, "用户同意隐私政策")
            agreePrivacyPolicy()
        }

        // 不同意按钮
        binding.disagreeButton.setOnClickListener {
            Log.d(TAG, "用户不同意隐私政策")
            disagreePrivacyPolicy()
        }
    }

    /**
     * 用户同意隐私政策
     * 保存同意状态并返回结果
     */
    private fun agreePrivacyPolicy() {
        // 保存用户同意状态到SharedPreferences
        val sharedPreferences = getSharedPreferences("privacy_policy", MODE_PRIVATE)
        sharedPreferences.edit()
            .putBoolean("agreed", true)
            .putLong("agree_time", System.currentTimeMillis())
            .apply()

        Log.d(TAG, "隐私政策同意状态已保存")

        // 返回同意结果
        setResult(RESULT_OK)
        finish()
    }

    /**
     * 用户不同意隐私政策
     * 返回不同意结果
     */
    private fun disagreePrivacyPolicy() {
        Log.d(TAG, "用户不同意隐私政策")

        // 返回不同意结果
        setResult(RESULT_CANCELED)
        finish()
    }

    /**
     * 禁用返回键
     * 用户必须明确选择同意或不同意
     */
    override fun onBackPressed() {
        Log.d(TAG, "用户按返回键，但隐私政策必须明确选择")
        // 不调用super.onBackPressed()，禁用返回键
    }
}
