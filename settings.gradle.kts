pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        // GroMore
        maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        // 荣耀仓库（广告SDK需要）
        maven { url = uri("https://developer.hihonor.com/repo") }
        // adset
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }
        // 本地AAR文件支持
        flatDir {
            dirs("../app/libs")
        }
    }
}

rootProject.name = "MountainSurvival"
include(":app")
