<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ad_layout"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal">

        <com.kc.openset.view.OSETNativeContainerView
            android:id="@+id/fl_material"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            tools:layout_width="100dp">

            <com.kc.openset.view.rounded.ODRoundedImageView
                android:id="@+id/iv_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                app:oset_riv_corner_radius="5dp"
                tools:src="#000000" />

            <TextureView
                android:id="@+id/texture_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:visibility="gone" />

            <com.kc.openset.view.OSETShakeView
                android:id="@+id/oset_shake_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="invisible" />

            <ImageView
                android:id="@+id/iv_voice"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_margin="5dp"
                android:src="@mipmap/oset_od_voiced"
                android:visibility="invisible" />
        </com.kc.openset.view.OSETNativeContainerView>

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_desc"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#C3000000"
                    android:textSize="12sp"
                    tools:text="广告描述广告描述广告描述广告描述广告描述广告描述" />

                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/oset_bg_black_translucent"
                    android:src="@mipmap/oset_od_close" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="广告标题广告标题" />

                <LinearLayout
                    android:id="@+id/ll_ad_logo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|right">

                    <include layout="@layout/oset_api_ad_logo" />
                </LinearLayout>

                <TextView
                    android:id="@+id/btn_download"
                    android:layout_width="wrap_content"
                    android:layout_height="22dp"
                    android:background="@drawable/oset_btn_bg_creative"
                    android:gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:textColor="#B22222"
                    android:textSize="12sp"
                    tools:text="点击下载" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>