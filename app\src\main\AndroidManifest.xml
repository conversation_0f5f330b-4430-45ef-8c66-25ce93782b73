<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 必要权限：应用基本功能所需 -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <!-- 广告优化权限：可选，用于提升广告效果 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>

    <!-- 以下权限已移除，因为单机游戏不需要这些敏感权限 -->
    <!-- <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/> -->
    <!-- <uses-permission android:name="android.permission.READ_PHONE_STATE"/> -->
    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/> -->
    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/> -->
    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/> -->

    <application
        android:name=".MountainSurvivalApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MountainSurvival"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31"
        tools:replace="android:allowBackup">
        <!-- 开始界面 - 启动Activity -->
        <activity
            android:name=".StartActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.MountainSurvival">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 隐私政策界面 -->
        <activity
            android:name=".PrivacyPolicyActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.MountainSurvival" />

        <!-- 主游戏界面 -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.MountainSurvival" />

        <!-- Support库FileProvider 兼容广告SDK -->
        <provider
            android:name="android.support.v4.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 广告SDK专用的FileProvider -->
        <provider
            android:name="android.support.v4.content.FileProvider"
            android:authorities="${applicationId}.osetfileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/oset_file_paths" />
        </provider>
    </application>

</manifest>