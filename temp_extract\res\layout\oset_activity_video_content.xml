<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:src="@drawable/oset_image_back"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rl_down"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical|left"
        android:layout_marginLeft="10dp"
        android:layout_marginBottom="20dp">

        <com.kc.openset.util.CircularProgressView
            android:id="@+id/cpv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:OSETbackColor="@android:color/white"
            app:OSETbackWidth="5dp"
            app:OSETprogColor="@android:color/holo_orange_dark"
            app:OSETprogWidth="5dp"
            app:OSETprogress="0" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@mipmap/oset_timeover_img" />
    </RelativeLayout>

</FrameLayout>