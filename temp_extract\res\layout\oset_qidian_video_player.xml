<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:orientation="vertical">

    <com.kc.openset.view.rounded.ODRoundedImageView
        android:id="@+id/iv_cover_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="invisible" />

    <TextureView
        android:id="@+id/texture_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center" />

    <ImageView
        android:id="@+id/iv_voice"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="20dp"
        android:src="@mipmap/oset_od_voiced"
        android:visibility="invisible" />

    <ImageView
        android:id="@+id/iv_replay"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_gravity="left|bottom"
        android:layout_margin="10dp"
        android:background="@drawable/oset_bg_black_translucent"
        android:padding="5dp"
        android:src="@mipmap/oset_od_replay"
        android:visibility="invisible" />
</FrameLayout>