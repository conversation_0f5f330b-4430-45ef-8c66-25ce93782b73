<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="5dp">

        <ImageView
            android:id="@+id/oset_ks_app_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:src="@mipmap/oset_ks_test_app_default_icon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/oset_ks_app_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="快手APP"
                android:textColor="#222222"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/oset_ks_app_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="记录美好生活"
                android:textColor="#9C9C9C"
                android:textSize="11dp" />
        </LinearLayout>

        <TextView
            android:id="@+id/oset_ks_app_download_btn"
            android:layout_width="64dp"
            android:layout_height="24dp"
            android:background="@drawable/oset_ks_native_item_btn_bg"
            android:gravity="center"
            android:text="立即下载"
            android:textColor="#168FFF"
            android:textSize="11dp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp">

        <ImageView
            android:id="@+id/oset_ks_ksad_logo_icon"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="2dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/oset_ks_ksad_logo_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/oset_ks_ksad_logo_icon"
            android:fontFamily="sans-serif-condensed-light"
            android:gravity="center_vertical"
            android:paddingLeft="2dp"
            android:paddingRight="3dp"
            android:text="广告"
            android:textColor="#9C9C9C"
            android:textSize="10sp"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/oset_ks_ad_dislike"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/oset_banner_close" />
    </RelativeLayout>
</LinearLayout>
