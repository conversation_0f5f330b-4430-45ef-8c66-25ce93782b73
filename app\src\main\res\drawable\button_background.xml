<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#2d5aa0" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#4a90e2" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#4a90e2" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#6bb6ff" />
        </shape>
    </item>
</selector>
