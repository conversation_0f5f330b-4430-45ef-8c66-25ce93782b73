<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/iv_sigmob_native_video"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:background="#000000" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <ImageView
            android:id="@+id/iv_sigmob_native_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_centerVertical="true" />

        <TextView
            android:id="@+id/tv_sigmob_native_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_toRightOf="@id/iv_sigmob_native_icon"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="标题标题标题标题标题标题标题标题标题标题标题题标题标题标题"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_sigmob_native_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="5dp"
            android:layout_toRightOf="@id/iv_sigmob_native_icon"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:text="内容内容内容内容内容内容内容内容内容内容内容内容内容内容"
            android:textSize="12sp" />

        <Button
            android:id="@+id/btn_sigmob_native_act"
            android:layout_width="wrap_content"
            android:layout_height="26dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="5dp"
            android:layout_marginRight="35dp"
            android:background="@drawable/oset_sigmob_native_btn_bg"
            android:text="立即下载" />

        <ImageView
            android:id="@+id/iv_sigmob_native_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignTop="@id/btn_sigmob_native_act"
            android:layout_alignBottom="@id/btn_sigmob_native_act"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:src="@drawable/oset_banner_close" />
    </RelativeLayout>
</LinearLayout>