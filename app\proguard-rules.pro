# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 神蓍广告SDK混淆规则
-keep class com.kc.openset.** { *; }
-dontwarn com.kc.openset.**

# 保护广告ID常量（混淆字段名但保留值）
-keepclassmembers class com.ainative.mountainsurvival.MountainSurvivalApplication {
    public static final java.lang.String APP_KEY;
    public static final java.lang.String REWARD_VIDEO_AD_ID;
}

# 保护游戏数据模型类（用于JSON反序列化）
-keep class com.ainative.mountainsurvival.GameEvent { *; }
-keep class com.ainative.mountainsurvival.Choice { *; }
-keep class com.ainative.mountainsurvival.RandomChoice { *; }
-keep class com.ainative.mountainsurvival.EventContainer { *; }
-keep class com.ainative.mountainsurvival.GameState { *; }
-keep class com.ainative.mountainsurvival.GameManager$ChoiceResult { *; }
-keep class com.ainative.mountainsurvival.GameManager$GameOverResult { *; }
-keep class com.ainative.mountainsurvival.GameManager$NightSettlementResult { *; }

# 保护Gson相关
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.** { *; }
-dontwarn com.google.gson.**

# 保护所有数据类的字段名（防止混淆影响JSON解析）
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# 保护Kotlin数据类
-keep class kotlin.Metadata { *; }
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}

# 保护游戏核心类
-keep class com.ainative.mountainsurvival.GameEngine { *; }
-keep class com.ainative.mountainsurvival.GameManager { *; }
-keep class com.ainative.mountainsurvival.GameEventUtils { *; }

# 保护所有游戏相关的数据类和枚举
-keep class com.ainative.mountainsurvival.**$* { *; }

# 保护反射相关
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# FileProvider相关
-keep class android.support.v4.content.FileProvider { *; }
-keep class * extends android.support.v4.content.FileProvider

# 广告SDK的FileProvider
-keep class com.kc.openset.util.OSETFileProvider { *; }

# 保护广告相关的接口和回调
-keep interface com.kc.openset.** { *; }
-keep class * implements com.kc.openset.** { *; }

# OAID相关
-keep class com.kc.openset.oaid.** { *; }
-dontwarn com.kc.openset.oaid.**

# 网络请求相关
-keep class okhttp3.** { *; }
-dontwarn okhttp3.**

# 阿里云日志SDK
-keep class com.aliyun.sls.** { *; }
-dontwarn com.aliyun.sls.**

#-------------- AdSet start-------------
-keep class com.kc.openset.**{*;}
-keep class com.od.**{*;}
-dontwarn com.kc.openset.**

-keep @com.qihoo.SdkProtected.OSETSDK.Keep class **{*;}
-keep,allowobfuscation interface com.qihoo.SdkProtected.OSETSDK.Keep
#-------------- AdSet end-------------

#----------log start--------------
-keep class com.aliyun.sls.android.producer.** { *; }
-keep interface com.aliyun.sls.android.producer.* { *; }
#----------log end--------------

#-------------- oaid start-------------
-keep class com.bun.supplier.** {*;}
-dontwarn com.bun.supplier.core.**
-keep class XI.**{*;}
-keep class com.bun.miitmdid.**{*;}

-keep class com.asus.msa.**{*;}
-keep class com.bun.**{*;}
-keep class com.huawei.hms.ads.identifier.**{*;}
-keep class com.netease.nis.sdkwrapper.**{*;}
-keep class com.samsung.android.deviceidservice.**{*;}
-keep class com.zui.**{*;}
-keep class XI.**{*;}
#-------------- oaid end-------------

#-------------- 百度 start-------------
-dontwarn com.baidu.mobads.sdk.api.**
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class com.baidu.mobads.** { *; }
-keep class com.style.widget.** {*;}
-keep class com.component.** {*;}
-keep class com.baidu.ad.magic.flute.** {*;}
-keep class com.baidu.mobstat.forbes.** {*;}
#-------------- 百度 end-------------

#-------------- 快手 start-------------
-keep class org.chromium.** {*;}
-keep class org.chromium.** { *; }
-keep class aegon.chrome.** { *; }
-keep class com.kwai.**{ *; }
#-------------- 快手 end-------------

# --- 统一处理第三方SDK的编译警告 ---

# GroMore / 穿山甲 SDK (Pangle SDK)
-dontwarn com.bykv.**
-dontwarn com.byted.**
-dontwarn com.bytedance.**
-dontwarn com.ss.**
-dontwarn com.volcengine.**
-dontwarn ms.bz.**

# Wind SDK
-dontwarn com.sigmob.**

