# 安全属性委托系统

## 概述

这个安全属性委托系统是为了防止像GG修改器这样的内存修改工具对游戏进行作弊而设计的。它使用Kotlin的属性委托特性，提供了一个优雅且强大的解决方案来保护关键游戏数值。

## 核心特性

### 🔐 数值混淆
- 实际存储的值不是原始值，而是与固定密钥进行异或运算后的结果
- 即使攻击者找到内存地址，看到的也是混淆后的无意义数值

### ✅ 校验和保护
- 每个受保护的值都有对应的校验和
- 校验和基于原始值通过独立算法生成
- 每次读取时都会验证数据完整性

### 🚨 篡改检测
- 在每次`getValue`调用时执行完整的校验流程
- 一旦检测到篡改，立即触发自定义的处理逻辑
- 可以选择记录日志、重置数据或退出游戏

### 🎯 Kotlin语言习惯
- 使用`by`关键字的属性委托，代码简洁优雅
- 对业务代码完全透明，像操作普通属性一样使用
- 支持所有标准的属性操作（读取、写入、数学运算等）

## 使用方法

### 1. 基本用法

```kotlin
class PlayerData {
    // 定义篡改检测处理函数
    private fun onTamperDetected() {
        Log.e("Security", "检测到数据篡改！")
        // 自定义处理逻辑
    }
    
    // 使用安全属性委托
    var health: Int by secureInt(100, ::onTamperDetected)
    var score: Int by secureInt(0, ::onTamperDetected)
    var level: Float by secureFloat(1.0f, ::onTamperDetected)
}
```

### 2. 像普通属性一样使用

```kotlin
val player = PlayerData()

// 读取值
println("当前血量: ${player.health}")

// 设置值
player.health = 80

// 数学运算
player.health += 20
player.health *= 2

// 条件判断
if (player.health < 50) {
    println("血量过低！")
}
```

### 3. 自定义篡改处理

```kotlin
class GameManager {
    private fun handleCheatDetection() {
        // 记录作弊行为
        Log.w("AntiCheat", "检测到作弊行为")
        
        // 可选的处理方式：
        // 1. 重置游戏数据
        // 2. 显示警告对话框
        // 3. 上报到服务器
        // 4. 退出游戏
        
        exitProcess(1)
    }
    
    var coins: Int by secureInt(1000, ::handleCheatDetection)
    var experience: Int by secureInt(0, ::handleCheatDetection)
}
```

## 支持的数据类型

### SecureIntDelegate
- 保护`Int`类型的数值
- 适用于血量、分数、等级等整数值

### SecureFloatDelegate  
- 保护`Float`类型的数值
- 适用于温度、速度、倍率等浮点数值

### 便捷工厂函数
- `secureInt(initialValue, onTamperDetected)` - 创建安全Int委托
- `secureFloat(initialValue, onTamperDetected)` - 创建安全Float委托

## 安全机制详解

### 混淆算法
```kotlin
// 存储时：混淆值 = 原始值 XOR 密钥
obfuscatedValue = realValue xor encryptionKey

// 读取时：原始值 = 混淆值 XOR 密钥  
realValue = obfuscatedValue xor encryptionKey
```

### 校验和算法
```kotlin
// 校验和 = 原始值的哈希码 * 31 + 盐值
checksum = realValue.hashCode() * 31 + checksumSalt
```

### 篡改检测流程
1. 从混淆值恢复原始值
2. 用恢复的原始值重新计算校验和
3. 比较新校验和与存储的校验和
4. 如果不匹配，触发篡改检测回调

## 最佳实践

### ✅ 推荐做法
- 对所有关键游戏数值使用安全委托
- 提供有意义的篡改检测处理逻辑
- 在篡改检测中记录详细的日志信息
- 考虑多层防护策略

### ❌ 避免的做法
- 不要在篡改检测中忽略错误
- 不要对非关键数据过度使用（影响性能）
- 不要在篡改检测中执行耗时操作
- 不要暴露内部的混淆机制

## 性能考虑

- 每次属性访问都会进行校验，有轻微的性能开销
- 建议只对真正重要的数值使用安全委托
- 对于频繁访问的属性，考虑缓存策略

## 示例项目

查看以下文件了解完整的使用示例：
- `PlayerStatus.kt` - 基本使用示例
- `SecureGameState.kt` - 游戏状态保护示例  
- `PlayerStatusExample.kt` - 详细的使用演示

## 注意事项

1. **兼容性**: 需要Kotlin 1.3+支持属性委托
2. **调试**: 在调试模式下可能需要特殊处理
3. **序列化**: 需要自定义序列化逻辑来处理委托属性
4. **反射**: 通过反射访问可能绕过保护机制

## 扩展性

这个系统可以轻松扩展支持其他数据类型：
- `SecureStringDelegate` - 保护字符串
- `SecureBooleanDelegate` - 保护布尔值
- `SecureListDelegate` - 保护列表数据

只需要实现`ReadWriteProperty<Any?, T>`接口即可。
