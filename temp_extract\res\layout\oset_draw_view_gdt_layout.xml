<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <FrameLayout
        android:id="@+id/fl_media"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="15dp"
        android:layout_marginBottom="20dp"
        android:background="#ffffff">


        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="5dp"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:layout_toRightOf="@id/iv_icon"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="标题"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="35dp"
            android:layout_marginRight="30dp"
            android:layout_toRightOf="@id/iv_icon"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="神蓍广告神蓍广告神蓍广告神蓍广告神蓍广告神蓍广告神蓍广告神蓍广告神蓍广告"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_ad_source"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:text="快手"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/iv_ad_source_img"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginLeft="5dp"
            android:visibility="gone"/>


        <Button
            android:id="@+id/btn_action"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="15dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/oset_gdt_btn_bg"
            android:text="立即下载"
            android:textColor="#ffffff" />
    </RelativeLayout>


    <LinearLayout
        android:id="@+id/ll_app_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:background="#22000000"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用名称"
            android:textColor="#ccffffff"
            android:textSize="10sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="11dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="1dp"
            android:background="#ccffffff" />

        <TextView
            android:id="@+id/tv_app_auother"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:ellipsize="end"
            android:maxEms="10"
            android:singleLine="true"
            android:text="开发者开发者开发者开发者开发者"
            android:textColor="#ccffffff"
            android:textSize="10sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="11dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="1dp"
            android:background="#ccffffff" />

        <TextView
            android:id="@+id/tv_app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:text="0.0.0"
            android:textColor="#ccffffff"
            android:textSize="10sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="11dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="1dp"
            android:background="#ccffffff" />

        <TextView
            android:id="@+id/tv_app_permission"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:text="权限"
            android:textColor="#ccffffff"
            android:textSize="10sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="11dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="1dp"
            android:background="#ccffffff" />

        <TextView
            android:id="@+id/tv_app_privacy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:text="隐私"
            android:textColor="#ccffffff"
            android:textSize="10sp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="11dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="1dp"
            android:background="#ccffffff" />

        <TextView
            android:id="@+id/tv_app_function"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:text="功能"
            android:textColor="#ccffffff"
            android:textSize="10sp"/>

    </LinearLayout>
</RelativeLayout>