<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ad_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <ImageView
        android:id="@+id/iv_image_blur"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextureView
        android:id="@+id/texture_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_voice"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="40dp"
        android:src="@mipmap/oset_od_voiced"
        android:visibility="invisible" />

    <com.kc.openset.sdk.apiad.ad_view.CountdownView
        android:id="@+id/countdown_view"
        android:layout_width="60dp"
        android:layout_height="25dp"
        android:layout_gravity="right"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="15dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/oset_bg_black_translucent"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:orientation="horizontal">

        <include layout="@layout/oset_api_ad_privacy_white" />
    </LinearLayout>

    <com.kc.openset.view.OSETShakeView
        android:id="@+id/oset_shake_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_margin="30dp" />

    <LinearLayout
        android:id="@+id/ll_ad_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|right">

        <include layout="@layout/oset_api_ad_logo" />
    </LinearLayout>
</FrameLayout>