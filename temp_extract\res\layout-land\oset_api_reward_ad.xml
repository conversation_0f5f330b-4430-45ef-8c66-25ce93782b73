<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:keepScreenOn="true"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_image_blur"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2">

            <TextureView
                android:id="@+id/texture_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center" />

            <ImageView
                android:id="@+id/iv_cover_img"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:visibility="invisible" />

            <com.kc.openset.view.OSETShakeView
                android:id="@+id/oset_shake_view"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="bottom"
                android:layout_margin="30dp" />

            <SeekBar
                android:id="@+id/seek_bar"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@null"
                android:layout_gravity="bottom"
                android:thumb="@null"
                android:progressDrawable="@drawable/oset_video_seekbar"
                android:max="100"
                android:progress="0" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:orientation="horizontal">

                <include layout="@layout/oset_api_ad_privacy_white" />
            </LinearLayout>
        </FrameLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_weight="1"
            android:orientation="vertical">


            <LinearLayout
                android:id="@+id/ll_app_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_margin="10dp"
                android:background="@drawable/oset_bg_dialog_rule"
                android:orientation="vertical"
                android:padding="15dp">

                <ImageView
                    android:id="@+id/iv_app_icon"
                    android:layout_width="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_height="50dp"
                    android:layout_marginBottom="10dp"
                    android:src="#eeff" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:textColor="#000000"
                            android:textSize="18sp"
                            tools:text="火柴人觉醒（测试服）" />

                        <TextView
                            android:id="@+id/tv_desc"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:textColor="#C3000000"
                            android:textSize="14sp"
                            tools:text="萌新：HCR520 HCR555 HCR777" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:id="@+id/btn_download"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/oset_api_download_btn_bg"
                    android:gravity="center"
                    android:text="立即下载"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="35dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:src="@mipmap/oset_od_voiced"
            android:visibility="invisible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <include layout="@layout/oset_api_ad_logo" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="35dp"
        android:layout_marginEnd="15dp"
        android:layout_marginRight="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_countdown"
            android:layout_width="wrap_content"
            android:layout_height="25dp"
            android:background="@drawable/oset_bg_black_translucent"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:textColor="#ffffff"
            android:textSize="12sp"
            tools:text="奖励将于%s秒后发放" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="15dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/oset_bg_black_translucent"
            android:src="@mipmap/oset_od_close"
            android:visibility="gone" />
    </LinearLayout>

</FrameLayout>