<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ad_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:layout_marginBottom="5dp"
            android:maxLines="2"
            android:textColor="#000000"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="广告标题" />
    </LinearLayout>

    <com.kc.openset.view.OSETNativeContainerView
        android:id="@+id/fl_material"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/oset_bg_black_radius_5">

        <com.kc.openset.view.rounded.ODRoundedImageView
            android:id="@+id/iv_image_blur"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:oset_riv_corner_radius="5dp" />

        <com.kc.openset.view.rounded.ODRoundedImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:oset_riv_corner_radius="5dp" />

        <TextureView
            android:id="@+id/texture_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:orientation="horizontal">

            <include layout="@layout/oset_api_ad_privacy_white"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="20dp"
            android:src="@mipmap/oset_od_voiced"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="right|top"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/oset_bg_black_translucent"
            android:src="@mipmap/oset_od_close" />

        <com.kc.openset.view.OSETShakeView
            android:id="@+id/oset_shake_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

        <ImageView
            android:id="@+id/iv_replay"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_gravity="left|bottom"
            android:layout_margin="10dp"
            android:background="@drawable/oset_bg_black_translucent"
            android:padding="5dp"
            android:src="@mipmap/oset_od_replay"
            android:visibility="invisible" />

        <LinearLayout
            android:id="@+id/ll_ad_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right">

            <include layout="@layout/oset_api_ad_logo" />
        </LinearLayout>
    </com.kc.openset.view.OSETNativeContainerView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#C3000000"
            android:textSize="14sp"
            tools:text="广告描述" />

        <TextView
            android:id="@+id/btn_download"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:background="@drawable/oset_btn_bg_creative"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:textColor="#B22222"
            android:textSize="12sp"
            tools:text="点击下载" />
    </LinearLayout>
</LinearLayout>