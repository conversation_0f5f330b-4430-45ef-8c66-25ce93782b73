<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kc.openset" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="31" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <application>
        <activity android:name="com.kc.openset.activity.OSETWebViewActivity" />
        <activity android:name="com.kc.openset.activity.OSETNativeViewAdAppInfoWebViewActivity" />
        <activity
            android:name="com.kc.openset.sdk.apiad.OSETAPIRewardADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:theme="@style/ODFullscreen" />
        <activity
            android:name="com.kc.openset.sdk.apiad.OSETAPIInterstitialAdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:theme="@style/ODDialogStyle" />
        <activity android:name="com.kc.openset.video.VideoContentActivity" />

        <!-- openset -->
        <provider
            android:name="com.kc.openset.util.OSETFileProvider"
            android:authorities="${applicationId}.osetfileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/oset_filepath" />
        </provider>
    </application>

</manifest>