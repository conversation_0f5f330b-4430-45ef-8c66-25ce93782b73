<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr format="reference|dimension" name="adSetMaxHeight"/>
    <attr format="reference|dimension" name="adSetMaxWidth"/>
    <color name="oset_colorAccent">#FF4081</color>
    <color name="oset_colorPrimary">#3F51B5</color>
    <color name="oset_colorPrimaryDark">#303F9F</color>
    <color name="oset_colorWhite">#FFFFFF</color>
    <color name="oset_progress_red">#F35A49</color>
    <color name="oset_text_select">#5179e9</color>
    <color name="oset_text_type_select">#FF4500</color>
    <color name="oset_text_type_unselect">#3E454E</color>
    <color name="oset_text_unselect">#96979a</color>
    <color name="oset_weather_theme">#4662EA</color>
    <color name="oset_yd_video_bg">#f5f5f5</color>
    <item name="srl_tag" type="id"/>
    <string name="init">初始化</string>
    <string name="interstitialAd">插屏视频</string>
    <string name="interstitialImageAd">插屏图片</string>
    <string name="oset_function"><u>功能</u></string>
    <string name="oset_permission"><u>权限</u></string>
    <string name="oset_privacy"><u>隐私</u></string>
    <string name="rewardedAd">激励视频</string>
    <string name="splash_ad">开屏广告</string>
    <string formatted="false" name="srl_component_falsify">%s falsify area,\n Represents the height[%.1fdp] of drag at run time,\n It does not show anything.</string>
    <string name="srl_content_empty">The content view in SmartRefreshLayout is empty. Do you forget to add it in xml layout file?</string>
    <style name="AppTheme" parent="android:Theme.Holo.Light.NoActionBar">
        <!-- Customize your theme here. -->
    </style>
    <style name="ODDialogStyle" parent="Theme.AppCompat.Dialog">
        <!--  设置背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--设置是否有边框-->
        <item name="android:windowFrame">@null</item>
        <!--设置是否有标题栏-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="ODFullscreen" parent="android:Theme.NoTitleBar.Fullscreen">
        <!-- Customize your theme here. -->
    </style>
    <style name="ODTabTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style mce_bogus="1" name="OSETDialogAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/oset_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/oset_dialog_exit</item>
    </style>
    <style name="OSETDialogStyle" parent="Theme.AppCompat.Dialog">
        <!--  设置背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--设置是否有边框-->
        <item name="android:windowFrame">@null</item>
        <!--设置是否有标题栏-->
        <item name="android:windowNoTitle">true</item>
    </style>
    <style name="line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:orientation">horizontal</item>
    </style>
    <declare-styleable name="AdSetSizeLimitView">
        <!--最大高度-->
        <attr name="adSetMaxHeight"/>
        <!--最大宽度-->
        <attr name="adSetMaxWidth"/>
    </declare-styleable>
    <declare-styleable name="OSETCircularProgressView">
        <attr format="dimension" name="OSETbackWidth"/>    <!--背景圆环宽度-->
        <attr format="dimension" name="OSETprogWidth"/>    <!--进度圆环宽度-->
        <attr format="color" name="OSETbackColor"/>        <!--背景圆环颜色-->
        <attr format="color" name="OSETprogColor"/>        <!--进度圆环颜色-->
        <attr format="color" name="OSETprogStartColor"/>   <!--进度圆环开始颜色-->
        <attr format="color" name="OSETprogFirstColor"/>   <!--进度圆环结束颜色-->
        <attr format="integer" name="OSETprogress"/>       <!--圆环进度-->
    </declare-styleable>
    <declare-styleable name="OSETRoundedImageView">
        <attr format="dimension" name="oset_riv_corner_radius"/>
        <attr format="dimension" name="oset_riv_corner_radius_top_left"/>
        <attr format="dimension" name="oset_riv_corner_radius_top_right"/>
        <attr format="dimension" name="oset_riv_corner_radius_bottom_left"/>
        <attr format="dimension" name="oset_riv_corner_radius_bottom_right"/>
        <attr format="dimension" name="oset_riv_border_width"/>
        <attr format="color" name="oset_riv_border_color"/>
        <attr format="boolean" name="oset_riv_mutate_background"/>
        <attr format="boolean" name="oset_riv_oval"/>
        <attr name="android:scaleType"/>
        <attr name="oset_riv_tile_mode">
            <enum name="clamp" value="0"/>
            <enum name="repeat" value="1"/>
            <enum name="mirror" value="2"/>
        </attr>
        <attr name="oset_riv_tile_mode_x">
            <enum name="clamp" value="0"/>
            <enum name="repeat" value="1"/>
            <enum name="mirror" value="2"/>
        </attr>
        <attr name="oset_riv_tile_mode_y">
            <enum name="clamp" value="0"/>
            <enum name="repeat" value="1"/>
            <enum name="mirror" value="2"/>
        </attr>
    </declare-styleable>
</resources>