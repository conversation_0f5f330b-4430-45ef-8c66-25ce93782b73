package com.ainative.mountainsurvival

import com.ainative.mountainsurvival.security.secureFloat
import com.ainative.mountainsurvival.security.secureInt
import android.util.Log
import kotlin.system.exitProcess

/**
 * 玩家状态类，使用安全属性委托保护关键游戏数据
 * 
 * 这个类演示了如何使用SecureValueDelegate来保护游戏中的重要数值，
 * 防止内存修改工具（如GG修改器）进行作弊
 */
class PlayerStatus {
    
    companion object {
        private const val TAG = "PlayerStatus"
        
        /**
         * 检测到篡改时的处理函数
         * 这里可以根据需要自定义处理逻辑
         */
        private fun handleTamperDetection() {
            Log.e(TAG, "⚠️ 检测到内存篡改！游戏将退出以保护数据完整性")
            
            // 可以在这里添加更多的反作弊措施：
            // 1. 记录作弊行为到服务器
            // 2. 显示警告对话框
            // 3. 重置游戏数据
            // 4. 退出游戏
            
            // 这里选择退出游戏
            exitProcess(1)
        }
    }
    
    // 使用安全属性委托保护关键游戏数值
    
    /**
     * 玩家体温 (°C)
     * 正常范围: 36.0 - 37.5
     */
    var temperature: Float by secureFloat(36.5f, ::handleTamperDetection)
    
    /**
     * 玩家体力 (0-100)
     */
    var stamina: Int by secureInt(100, ::handleTamperDetection)
    
    /**
     * 食物储备 (0-100)
     */
    var food: Int by secureInt(50, ::handleTamperDetection)
    
    /**
     * 木柴储备 (0-100)
     */
    var firewood: Int by secureInt(30, ::handleTamperDetection)
    
    /**
     * 获取玩家状态摘要
     */
    fun getStatusSummary(): String {
        return """
            |玩家状态:
            |体温: ${String.format("%.1f", temperature)}°C
            |体力: $stamina/100
            |食物: $food/100
            |木柴: $firewood/100
        """.trimMargin()
    }
    
    /**
     * 检查玩家是否处于危险状态
     */
    fun isInDanger(): Boolean {
        return temperature < 35.0f || stamina < 20 || food < 10
    }
    
    /**
     * 消耗体力
     */
    fun consumeStamina(amount: Int) {
        stamina = maxOf(0, stamina - amount)
        Log.d(TAG, "体力消耗: $amount, 当前体力: $stamina")
    }
    
    /**
     * 消耗食物
     */
    fun consumeFood(amount: Int) {
        food = maxOf(0, food - amount)
        Log.d(TAG, "食物消耗: $amount, 当前食物: $food")
    }
    
    /**
     * 消耗木柴
     */
    fun consumeFirewood(amount: Int) {
        firewood = maxOf(0, firewood - amount)
        Log.d(TAG, "木柴消耗: $amount, 当前木柴: $firewood")
    }
    
    /**
     * 降低体温
     */
    fun decreaseTemperature(amount: Float) {
        temperature = maxOf(30.0f, temperature - amount)
        Log.d(TAG, "体温下降: ${String.format("%.1f", amount)}°C, 当前体温: ${String.format("%.1f", temperature)}°C")
    }
    
    /**
     * 恢复体力
     */
    fun restoreStamina(amount: Int) {
        stamina = minOf(100, stamina + amount)
        Log.d(TAG, "体力恢复: $amount, 当前体力: $stamina")
    }
    
    /**
     * 补充食物
     */
    fun addFood(amount: Int) {
        food = minOf(100, food + amount)
        Log.d(TAG, "食物补充: $amount, 当前食物: $food")
    }
    
    /**
     * 补充木柴
     */
    fun addFirewood(amount: Int) {
        firewood = minOf(100, firewood + amount)
        Log.d(TAG, "木柴补充: $amount, 当前木柴: $firewood")
    }
    
    /**
     * 提高体温（例如通过生火取暖）
     */
    fun increaseTemperature(amount: Float) {
        temperature = minOf(37.5f, temperature + amount)
        Log.d(TAG, "体温上升: ${String.format("%.1f", amount)}°C, 当前体温: ${String.format("%.1f", temperature)}°C")
    }
    
    /**
     * 重置所有状态到初始值
     */
    fun reset() {
        temperature = 36.5f
        stamina = 100
        food = 50
        firewood = 30
        Log.i(TAG, "玩家状态已重置")
    }
}
