<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="#FFFFFF"
    android:orientation="horizontal"
    android:padding="2dp">

    <RelativeLayout
        android:id="@+id/rl_media"
        android:layout_width="80dp"
        android:layout_height="46dp"
        android:gravity="center">

        <FrameLayout
            android:id="@+id/fl_ks_banner_video"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/oset_ks_media_bg_multiple_version" />

        <ImageView
            android:id="@+id/iv_ks_banner_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/oset_ks_media_bg_multiple_version" />

    </RelativeLayout>


    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="15dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_ks_banner_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="bottom"
                android:lines="1"
                android:maxEms="7"
                android:paddingBottom="2dp"
                android:textColor="#000000"
                tools:text="标题标题懂法守法标题标题" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_ks_banner_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:textSize="12sp"
            tools:text="下载ADSET，高收益，结算快，安全稳定可靠" />

    </LinearLayout>


    <Button
        android:id="@+id/btn_ks_banner_act"
        android:layout_width="80dp"
        android:layout_height="30dp"
        android:layout_gravity="center_vertical"
        android:background="@drawable/oset_ks_btn_act_sub_bg_multiple_version"
        android:text="立即下载"
        android:textColor="@android:color/white"
        android:textSize="12sp" />

    <ImageView
        android:id="@+id/iv_ks_banner_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/oset_banner_close" />
</LinearLayout>