<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/oset_bg_dialog_rule"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="15dp"
            android:layout_marginTop="15dp"
            android:gravity="center"
            android:text="观看完整视频即可获得奖励，确认要离开吗？"
            android:textColor="#000000"
            android:textSize="16sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="15dp"
            android:background="#22222222" />

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_exit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="放弃奖励"
                android:textColor="#000000"
                android:textSize="16sp" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#22222222" />

            <TextView
                android:id="@+id/tv_continue"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="继续观看"
                android:textColor="#4181EB"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>