<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ad_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.3">

            <com.kc.openset.view.rounded.ODRoundedImageView
                android:id="@+id/iv_image"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:adjustViewBounds="true"
                android:src="#000000"
                android:scaleType="centerCrop"
                app:oset_riv_corner_radius="5dp" />

            <LinearLayout
                android:id="@+id/ll_ad_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom">

                <include layout="@layout/oset_api_ad_logo" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_gravity="right|top"
                android:layout_margin="5dp"
                android:background="@drawable/oset_bg_black_translucent"
                android:src="@mipmap/oset_od_close" />
        </FrameLayout>

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:layout_weight="0.6"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#000000"
                android:textSize="14sp"
                tools:text="广告标题广告标题" />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#C3000000"
                android:textSize="13sp"
                tools:text="广告描述广告描述广告描述广告描述广告描述广告描述" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            </LinearLayout>

            <TextView
                android:id="@+id/btn_download"
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/oset_btn_bg_creative"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:textColor="#B22222"
                android:textSize="12sp"
                tools:text="点击下载" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>