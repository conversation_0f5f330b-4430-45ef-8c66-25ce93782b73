package com.ainative.mountainsurvival

import android.util.Log

/**
 * PlayerStatus使用示例
 * 
 * 这个文件展示了如何使用带有安全属性委托的PlayerStatus类
 */
object PlayerStatusExample {
    
    private const val TAG = "PlayerStatusExample"
    
    /**
     * 演示基本的属性读写操作
     */
    fun demonstrateBasicUsage() {
        Log.i(TAG, "=== 基本使用示例 ===")
        
        val player = PlayerStatus()
        
        // 读取初始状态
        Log.i(TAG, "初始状态:")
        Log.i(TAG, player.getStatusSummary())
        
        // 像操作普通属性一样修改值
        player.stamina = 80
        player.food = 60
        player.firewood = 40
        player.temperature = 36.8f
        
        Log.i(TAG, "\n修改后状态:")
        Log.i(TAG, player.getStatusSummary())
        
        // 检查危险状态
        if (player.isInDanger()) {
            Log.w(TAG, "⚠️ 玩家处于危险状态！")
        } else {
            Log.i(TAG, "✅ 玩家状态良好")
        }
    }
    
    /**
     * 演示游戏循环中的状态变化
     */
    fun demonstrateGameLoop() {
        Log.i(TAG, "\n=== 游戏循环示例 ===")
        
        val player = PlayerStatus()
        
        // 模拟游戏中的各种活动
        for (hour in 1..24) {
            Log.i(TAG, "\n--- 第 $hour 小时 ---")
            
            // 每小时自然消耗
            player.consumeStamina(5)
            player.consumeFood(3)
            player.decreaseTemperature(0.2f)
            
            // 根据时间进行不同活动
            when (hour % 6) {
                0 -> {
                    // 休息恢复体力
                    Log.i(TAG, "休息中...")
                    player.restoreStamina(20)
                }
                2 -> {
                    // 收集木柴
                    Log.i(TAG, "收集木柴...")
                    player.addFirewood(15)
                    player.consumeStamina(10)
                }
                4 -> {
                    // 寻找食物
                    Log.i(TAG, "寻找食物...")
                    player.addFood(20)
                    player.consumeStamina(15)
                }
                else -> {
                    // 生火取暖
                    if (player.firewood > 5) {
                        Log.i(TAG, "生火取暖...")
                        player.consumeFirewood(5)
                        player.increaseTemperature(1.0f)
                    }
                }
            }
            
            // 显示当前状态
            Log.d(TAG, "当前状态: 体温=${String.format("%.1f", player.temperature)}°C, " +
                    "体力=${player.stamina}, 食物=${player.food}, 木柴=${player.firewood}")
            
            // 检查危险状态
            if (player.isInDanger()) {
                Log.w(TAG, "⚠️ 警告：玩家处于危险状态！")
                break
            }
        }
        
        Log.i(TAG, "\n最终状态:")
        Log.i(TAG, player.getStatusSummary())
    }
    
    /**
     * 演示属性的直接访问（就像普通属性一样）
     */
    fun demonstrateDirectAccess() {
        Log.i(TAG, "\n=== 直接属性访问示例 ===")
        
        val player = PlayerStatus()
        
        // 直接读取属性值
        Log.i(TAG, "当前体温: ${player.temperature}°C")
        Log.i(TAG, "当前体力: ${player.stamina}")
        Log.i(TAG, "当前食物: ${player.food}")
        Log.i(TAG, "当前木柴: ${player.firewood}")
        
        // 直接设置属性值
        player.temperature = 35.5f
        player.stamina = 25
        player.food = 15
        player.firewood = 8
        
        Log.i(TAG, "\n设置新值后:")
        Log.i(TAG, "体温: ${player.temperature}°C")
        Log.i(TAG, "体力: ${player.stamina}")
        Log.i(TAG, "食物: ${player.food}")
        Log.i(TAG, "木柴: ${player.firewood}")
        
        // 进行数学运算
        player.stamina += 10
        player.food -= 5
        player.temperature *= 1.02f
        
        Log.i(TAG, "\n数学运算后:")
        Log.i(TAG, "体温: ${String.format("%.1f", player.temperature)}°C")
        Log.i(TAG, "体力: ${player.stamina}")
        Log.i(TAG, "食物: ${player.food}")
    }
    
    /**
     * 演示边界值处理
     */
    fun demonstrateBoundaryValues() {
        Log.i(TAG, "\n=== 边界值处理示例 ===")
        
        val player = PlayerStatus()
        
        // 测试极端值
        player.stamina = -10  // 应该被限制在合理范围内
        player.food = 150    // 应该被限制在合理范围内
        
        Log.i(TAG, "设置极端值后:")
        Log.i(TAG, "体力: ${player.stamina} (设置为-10)")
        Log.i(TAG, "食物: ${player.food} (设置为150)")
        
        // 测试温度边界
        player.temperature = 40.0f
        Log.i(TAG, "体温: ${player.temperature}°C (设置为40.0)")
        
        player.temperature = 20.0f
        Log.i(TAG, "体温: ${player.temperature}°C (设置为20.0)")
    }
}
