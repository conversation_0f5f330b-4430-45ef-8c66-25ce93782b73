<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_app_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:textColor="#80000000"
        android:textSize="10sp"
        tools:text="应用名称：小米 | 版本：1.0.2 | 开发者：北京比特漫步科技有限公司" />

    <TextView
        android:id="@+id/tv_app_function"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="web"
        android:gravity="center_vertical"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/oset_function"
        android:textColor="#80000000"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/tv_app_permission"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="web"
        android:gravity="center_vertical"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/oset_permission"
        android:textColor="#80000000"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/tv_app_privacy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="all"
        android:gravity="center_vertical"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/oset_privacy"
        android:textColor="#80000000"
        android:textSize="10sp" />
</LinearLayout>
