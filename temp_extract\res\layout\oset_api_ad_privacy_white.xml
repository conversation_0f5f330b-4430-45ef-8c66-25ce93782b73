<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="30dp"
    android:background="@drawable/oset_bg_black_top_to_bottom"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_app_info"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:textColor="#ffffff"
        android:textSize="9sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="应用名称：小米 | 版本：1.0.2 | 开发者：北京比特漫步科技有限公司" />

    <TextView
        android:id="@+id/tv_app_function"
        android:layout_width="30dp"
        android:layout_height="match_parent"
        android:autoLink="web"
        android:gravity="center"
        android:text="@string/oset_function"
        android:textColor="#ffffff"
        android:textSize="9sp" />

    <TextView
        android:id="@+id/tv_app_permission"
        android:layout_width="30dp"
        android:layout_height="match_parent"
        android:autoLink="web"
        android:gravity="center"
        android:text="@string/oset_permission"
        android:textColor="#ffffff"
        android:textSize="9sp" />

    <TextView
        android:id="@+id/tv_app_privacy"
        android:layout_width="30dp"
        android:layout_height="match_parent"
        android:autoLink="web"
        android:gravity="center"
        android:text="@string/oset_privacy"
        android:textColor="#ffffff"
        android:textSize="9sp" />
</LinearLayout>
