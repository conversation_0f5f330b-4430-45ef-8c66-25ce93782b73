<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/ad_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="45dp"
        android:layout_marginRight="45dp"
        android:background="@drawable/oset_bg_black_radius"
        android:orientation="vertical">

        <com.kc.openset.view.rounded.ODRoundedImageView
            android:id="@+id/iv_image_blur"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:oset_riv_corner_radius="10dp" />

        <com.kc.openset.view.rounded.ODRoundedImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:src="#ddff" />

        <TextureView
            android:id="@+id/texture_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_skip"
            android:layout_width="wrap_content"
            android:layout_height="25dp"
            android:layout_margin="15dp"
            android:background="@drawable/oset_bg_black_translucent"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="跳过"
            android:textColor="#ffffff"
            android:textSize="12sp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="15dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:src="@mipmap/oset_od_voiced"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="right"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/oset_bg_black_translucent"
            android:src="@mipmap/oset_od_close" />

        <LinearLayout
            android:id="@+id/ll_app_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/oset_bg_black_gradient"
            android:orientation="vertical">

            <com.kc.openset.view.OSETShakeView
                android:id="@+id/oset_shake_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <ImageView
                android:id="@+id/iv_app_icon"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                tools:src="#9889" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="20dp">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center_horizontal"
                    android:textColor="#ffffff"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="支付宝" />

                <TextView
                    android:id="@+id/tv_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:gravity="center_horizontal"
                    android:textColor="#CCFFFFFF"
                    android:textSize="12sp"
                    tools:text="快来抽盲盒，有红包拿哦" />

                <TextView
                    android:id="@+id/btn_download"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginLeft="50dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginRight="50dp"
                    android:layout_marginBottom="15dp"
                    android:background="@drawable/oset_api_download_btn_bg"
                    android:gravity="center"
                    android:text="立即下载"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom">

            <include layout="@layout/oset_api_ad_logo" />
        </LinearLayout>
    </FrameLayout>

    <TextView
        android:id="@+id/tv_auto_shutdown_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ad_layout"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="50dp"
        android:text="10秒后广告将自动关闭"
        android:textColor="#ffff"
        android:textSize="20sp"
        android:textStyle="bold"
        android:visibility="invisible" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:padding="5dp">

        <include layout="@layout/oset_api_ad_privacy" />
    </LinearLayout>

</RelativeLayout>