<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">


        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:padding="15dp"
            android:src="@drawable/oset_dial_back"
            android:tint="#000000" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignBottom="@id/iv_back"
            android:background="#CCCCCC" />

        <TextView
            android:id="@+id/tv_title"
            android:textSize="18sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="神蓍(隐私协议)"
            android:textColor="#000000"
            android:textStyle="bold" />
    </RelativeLayout>


    <WebView
        android:id="@+id/web"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ScrollView
        android:id="@+id/sl_permission"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_permission"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical" />
    </ScrollView>
</LinearLayout>