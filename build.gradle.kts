// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        // GroMore
        maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        // 荣耀
        maven { url = uri("https://developer.hihonor.com/repo") }
        // adset
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }
    }
}

plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
}