package com.ainative.mountainsurvival

import com.ainative.mountainsurvival.security.secureInt
import android.util.Log
import kotlin.system.exitProcess

/**
 * 安全版本的游戏状态类
 * 使用安全属性委托保护关键游戏数值，防止内存修改攻击
 */
class SecureGameState {
    
    companion object {
        private const val TAG = "SecureGameState"
        
        /**
         * 检测到篡改时的处理函数
         */
        private fun handleTamperDetection() {
            Log.e(TAG, "🚨 检测到游戏数据被篡改！")
            Log.e(TAG, "为了保护游戏公平性，游戏将立即退出")
            
            // 可以在这里添加更多反作弊措施：
            // 1. 记录作弊行为
            // 2. 上报到服务器
            // 3. 显示警告信息
            // 4. 重置游戏进度
            
            exitProcess(1)
        }
    }
    
    // 使用安全属性委托保护核心游戏数值
    
    /**
     * 体温 (Warmth) - 受保护
     * 范围: 0-100
     * 生命线，主要通过壁炉维持。降至0则游戏失败。
     */
    var warmth: Int by secureInt(80, ::handleTamperDetection)
    
    /**
     * 体力 (Stamina) - 受保护
     * 范围: 0-100
     * 行动力，执行大部分行动都需要消耗。可通过休息、进食恢复。
     */
    var stamina: Int by secureInt(100, ::handleTamperDetection)
    
    /**
     * 木柴 (Firewood) - 受保护
     * 范围: 0-50
     * 热量来源，夜晚壁炉会自动消耗。主要通过外出砍柴获得。
     */
    var firewood: Int by secureInt(5, ::handleTamperDetection)
    
    /**
     * 食物 (Food) - 受保护
     * 范围: 0-20
     * 体力来源，可在黄昏阶段选择"进食"来恢复大量体力。
     */
    var food: Int by secureInt(3, ::handleTamperDetection)
    
    /**
     * 房屋状况 (Cabin Integrity) - 受保护的隐藏数值
     * 范围: 0-100
     * 代表小屋的坚固程度。某些事件会降低它，玩家需要消耗资源修复。
     */
    var cabinIntegrity: Int by secureInt(40, ::handleTamperDetection)
    
    /**
     * 希望值 (Hope) - 受保护的隐藏数值
     * 范围: 0-100
     * 影响某些事件的文本描述和结局的文本。
     */
    var hope: Int by secureInt(50, ::handleTamperDetection)
    
    // 以下属性不需要特别保护，因为它们不是核心数值
    
    /**
     * 当前天数 (Current Day)
     * 游戏目标是存活7天
     */
    var currentDay: Int = 1
    
    /**
     * 特殊物品
     * 存储玩家获得的特殊物品（如信号枪）
     */
    var specialItems: MutableSet<String> = mutableSetOf()
    
    /**
     * 屋顶漏雪状态
     * 如果为true，每晚额外消耗15体温
     */
    var roofLeaking: Boolean = false
    
    /**
     * 特殊状态标记
     * 用于记录各种特殊状态和获得的技能
     */
    var specialStates: MutableMap<String, Boolean> = mutableMapOf()
    
    /**
     * 检查游戏是否结束
     * @return Pair<Boolean, String> - 第一个值表示是否结束，第二个值表示结束原因
     */
    fun checkGameOver(): Pair<Boolean, String> {
        return when {
            warmth <= 0 -> Pair(true, "体温过低")
            stamina <= 0 -> Pair(true, "体力耗尽")
            hope <= 0 -> Pair(true, "绝望而死")
            currentDay > 7 -> Pair(true, getVictoryType())
            else -> Pair(false, "")
        }
    }
    
    /**
     * 获取胜利类型
     * 根据最终状态决定胜利结局的类型
     */
    private fun getVictoryType(): String {
        return when {
            hope >= 80 && specialItems.contains("signal_gun") -> "完美救援"
            hope >= 70 -> "坚强意志"
            hope >= 40 -> "成功存活"
            else -> "勉强存活"
        }
    }
    
    /**
     * 应用效果到游戏状态
     * @param effects 效果映射表，键为属性名，值为变化量
     */
    fun applyEffects(effects: Map<String, Int>) {
        effects.forEach { (key, value) ->
            when (key) {
                "warmth" -> warmth = (warmth + value).coerceIn(0, 100)
                "stamina" -> stamina = (stamina + value).coerceIn(0, 100)
                "firewood" -> firewood = (firewood + value).coerceIn(0, 50)
                "food" -> food = (food + value).coerceIn(0, 20)
                "cabin_integrity" -> cabinIntegrity = (cabinIntegrity + value).coerceIn(0, 100)
                "hope" -> hope = (hope + value).coerceIn(0, 100)
            }
        }
    }
    
    /**
     * 夜晚结算
     * 根据游戏规则自动处理夜晚的数值变化
     */
    fun nightTimeSettlement() {
        // 基础体力消耗
        stamina = (stamina - 12).coerceAtLeast(0)

        if (firewood >= 5) {
            // 木柴足够：消耗5木柴，增加体温
            firewood = (firewood - 5).coerceAtLeast(0)
            warmth = (warmth + 10).coerceAtMost(100)
        } else {
            // 木柴不足：体温下降
            warmth = (warmth - 20).coerceAtLeast(0)
        }

        // 如果屋顶漏雪，额外消耗体温
        if (roofLeaking) {
            warmth = (warmth - 15).coerceAtLeast(0)
        }

        // 根据小屋状况影响体温保持
        when {
            cabinIntegrity >= 70 -> warmth = (warmth + 5).coerceAtMost(100)
            cabinIntegrity <= 20 -> warmth = (warmth - 10).coerceAtLeast(0)
        }

        // 希望值影响体力恢复
        when {
            hope >= 80 -> stamina = (stamina + 5).coerceAtMost(100)
            hope <= 20 -> stamina = (stamina - 5).coerceAtLeast(0)
        }
    }
    
    /**
     * 进食
     * 消耗食物，恢复体力
     */
    fun eat(): Boolean {
        return if (food > 0) {
            food -= 1
            stamina = (stamina + 35).coerceAtMost(100)
            // 进食也能稍微提升希望值
            hope = (hope + 5).coerceAtMost(100)
            true
        } else {
            false
        }
    }
    
    /**
     * 获取状态显示文本
     * @param statName 状态名称
     * @return 格式化的显示文本（包含emoji图标）
     */
    fun getStatusText(statName: String): String {
        return when (statName) {
            "warmth" -> "🔥 $warmth"
            "stamina" -> "💪 $stamina"
            "firewood" -> "🪵 $firewood"
            "food" -> "🍖 $food"
            else -> ""
        }
    }
    
    /**
     * 设置特殊状态
     */
    fun setSpecialState(stateName: String, value: Boolean) {
        specialStates[stateName] = value
    }
    
    /**
     * 检查特殊状态
     */
    fun hasSpecialState(stateName: String): Boolean {
        return specialStates[stateName] == true
    }
    
    /**
     * 获取生存难度评估
     */
    fun getSurvivalDifficulty(): String {
        val totalScore = warmth + stamina + (firewood * 5) + (food * 10) + hope + cabinIntegrity
        return when {
            totalScore >= 400 -> "优秀"
            totalScore >= 300 -> "良好"
            totalScore >= 200 -> "困难"
            totalScore >= 100 -> "危险"
            else -> "绝望"
        }
    }
    
    /**
     * 获取所有受保护数值的摘要
     */
    fun getSecureValuesSummary(): String {
        return """
            |安全游戏状态:
            |🔥 体温: $warmth/100
            |💪 体力: $stamina/100  
            |🪵 木柴: $firewood/50
            |🍖 食物: $food/20
            |🏠 小屋状况: $cabinIntegrity/100
            |💝 希望值: $hope/100
            |📅 第 $currentDay 天
            |📊 生存评估: ${getSurvivalDifficulty()}
        """.trimMargin()
    }
}
