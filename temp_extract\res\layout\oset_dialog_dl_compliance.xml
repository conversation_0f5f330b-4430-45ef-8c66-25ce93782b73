<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="45dp"
        android:layout_marginRight="45dp"
        android:background="@drawable/oset_bg_dialog_rule"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="15dp">

        <com.kc.openset.view.rounded.ODRoundedImageView
            android:id="@+id/iv_icon"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:src="#ddff"
            app:oset_riv_corner_radius="10dp" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="#000000"
            android:textSize="18sp"
            tools:text="火柴人觉醒（测试服）" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:textColor="#000000"
            android:textSize="14sp"
            tools:text="萌新HCR520 HCR555 HCR777萌新萌新" />


        <include layout="@layout/oset_api_ad_privacy" />

        <TextView
            android:id="@+id/btn_download"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginLeft="50dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="50dp"
            android:background="@drawable/oset_api_download_btn_bg"
            android:gravity="center"
            android:text="立即下载"
            android:textColor="#FFFFFF"
            android:textSize="18sp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="top|right"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="60dp"
        android:layout_marginRight="60dp"
        android:background="@drawable/oset_bg_black_translucent"
        android:src="@mipmap/oset_od_close" />

</FrameLayout>