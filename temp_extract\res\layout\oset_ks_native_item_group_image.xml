<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/oset_ks_ad_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/oset_ks_native_ad_bg"
    android:orientation="vertical"
    tools:ignore="ContentDescription,SpUsage,RtlHardcoded">

    <FrameLayout
        android:id="@+id/oset_ks_video_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            tools:ignore="UselessParent">

            <ImageView
                android:id="@+id/oset_ks_ad_image_left"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:scaleType="centerInside" />

            <ImageView
                android:id="@+id/oset_ks_ad_image_mid"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="4dp"
                android:layout_weight="1"
                android:scaleType="centerInside" />


            <ImageView
                android:id="@+id/oset_ks_ad_image_right"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:scaleType="centerInside" />
        </LinearLayout>
    </FrameLayout>

    <include
        android:id="@+id/oset_ks_ad_actionBar_container"
        layout="@layout/oset_ks_native_item_app_download"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>