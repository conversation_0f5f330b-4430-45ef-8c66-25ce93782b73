package com.ainative.mountainsurvival

import android.app.Activity
import android.util.Log
import com.kc.openset.ad.reward.OSETRewardVideo
import com.kc.openset.ad.listener.OSETRewardAdLoadListener
import com.kc.openset.ad.reward.OSETRewardAd
import com.kc.openset.ad.listener.OSETRewardListener

/**
 * 广告管理器
 * 负责处理激励视频广告的加载和显示
 */
object AdManager {
    private const val TAG = "AdManager"
    
    /**
     * 资源类型枚举
     */
    enum class ResourceType(val displayName: String, val rewardAmount: Int) {
        WARMTH("体温", 50),
        STAMINA("体力", 50),
        FIREWOOD("木柴", 20),
        FOOD("食物", 10)
    }

    /**
     * 广告回调接口
     */
    interface AdCallback {
        fun onAdLoadSuccess()
        fun onAdLoadFailed(error: String)
        fun onAdShowSuccess()
        fun onAdShowFailed(error: String)
        fun onAdRewarded(resourceType: ResourceType)
        fun onAdClosed()
    }

    /**
     * 复活广告回调接口
     */
    interface ReviveAdCallback {
        fun onAdLoadSuccess()
        fun onAdLoadFailed(error: String)
        fun onAdShowSuccess()
        fun onAdShowFailed(error: String)
        fun onReviveSuccess()
        fun onAdClosed()
    }

    /**
     * 显示激励视频广告获取资源
     * @param activity 当前Activity
     * @param resourceType 资源类型
     * @param callback 广告回调
     */
    fun showRewardVideoForResource(
        activity: Activity,
        resourceType: ResourceType,
        callback: AdCallback
    ) {
        Log.d(TAG, "开始加载激励视频广告，资源类型: ${resourceType.displayName}")
        Log.d(TAG, "使用广告位ID: ${MountainSurvivalApplication.REWARD_VIDEO_AD_ID}")
        Log.d(TAG, "应用包名: ${activity.packageName}")

        // 检查广告SDK是否已初始化
        val application = activity.application as MountainSurvivalApplication
        if (!application.isAdSDKInitialized()) {
            Log.w(TAG, "广告SDK未初始化，无法显示广告")
            callback.onAdLoadFailed("广告SDK未初始化")
            return
        }

        try {
            OSETRewardVideo.getInstance()
                .setContext(activity)
                .setUserId("mountain_survival_user")
                .setPosId(MountainSurvivalApplication.REWARD_VIDEO_AD_ID)
                .loadAd(object : OSETRewardAdLoadListener {
                    override fun onLoadSuccess(rewardAd: OSETRewardAd?) {
                        Log.d(TAG, "激励视频广告加载成功")
                        callback.onAdLoadSuccess()

                        // 检查广告是否有效
                        if (rewardAd?.isUsable == true) {
                            // 显示广告
                            rewardAd.showAd(activity, object : OSETRewardListener {
                                override fun onShow() {
                                    Log.d(TAG, "激励视频广告开始播放")
                                    callback.onAdShowSuccess()
                                }

                                override fun onError(errorCode: String?, errorMessage: String?) {
                                    Log.e(TAG, "激励视频广告播放失败: $errorCode - $errorMessage")
                                    callback.onAdShowFailed("广告播放失败: $errorMessage")
                                }

                                override fun onClick() {
                                    Log.d(TAG, "激励视频广告被点击")
                                }

                                override fun onClose() {
                                    Log.d(TAG, "激励视频广告关闭")
                                    callback.onAdClosed()
                                }

                                override fun onVideoStart() {
                                    Log.d(TAG, "激励视频开始播放")
                                }

                                override fun onVideoEnd() {
                                    Log.d(TAG, "激励视频播放完成")
                                }

                                override fun onReward() {
                                    Log.d(TAG, "激励视频广告奖励触发，资源类型: ${resourceType.displayName}")
                                    callback.onAdRewarded(resourceType)
                                }

                                override fun onServiceResponse(i: Int) {
                                    Log.d(TAG, "激励视频广告服务端回调: $i")
                                }
                            })
                        } else {
                            Log.e(TAG, "激励视频广告无效")
                            callback.onAdShowFailed("广告无效")
                        }
                    }

                    override fun onLoadFail(errorCode: String?, errorMessage: String?) {
                        Log.e(TAG, "激励视频广告加载失败: $errorCode - $errorMessage")
                        callback.onAdLoadFailed("广告加载失败: $errorMessage")
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "激励视频广告异常", e)
            callback.onAdLoadFailed("广告异常: ${e.message}")
        }
    }

    /**
     * 显示复活广告
     * @param activity 当前Activity
     * @param callback 复活广告回调
     */
    fun showReviveAd(
        activity: Activity,
        callback: ReviveAdCallback
    ) {
        Log.d(TAG, "开始加载复活广告")

        // 检查广告SDK是否已初始化
        val application = activity.application as MountainSurvivalApplication
        if (!application.isAdSDKInitialized()) {
            Log.w(TAG, "广告SDK未初始化，无法显示复活广告")
            callback.onAdLoadFailed("广告SDK未初始化")
            return
        }

        try {
            OSETRewardVideo.getInstance()
                .setContext(activity)
                .setUserId("mountain_survival_user")
                .setPosId(MountainSurvivalApplication.REWARD_VIDEO_AD_ID)
                .loadAd(object : OSETRewardAdLoadListener {
                    override fun onLoadSuccess(rewardAd: OSETRewardAd?) {
                        Log.d(TAG, "复活广告加载成功")
                        callback.onAdLoadSuccess()

                        // 检查广告是否有效
                        if (rewardAd?.isUsable == true) {
                            // 显示广告
                            rewardAd.showAd(activity, object : OSETRewardListener {
                                override fun onShow() {
                                    Log.d(TAG, "复活广告开始播放")
                                    callback.onAdShowSuccess()
                                }

                                override fun onError(errorCode: String?, errorMessage: String?) {
                                    Log.e(TAG, "复活广告播放失败: $errorCode - $errorMessage")
                                    callback.onAdShowFailed("复活广告播放失败: $errorMessage")
                                }

                                override fun onClick() {
                                    Log.d(TAG, "复活广告被点击")
                                }

                                override fun onClose() {
                                    Log.d(TAG, "复活广告关闭")
                                    callback.onAdClosed()
                                }

                                override fun onVideoStart() {
                                    Log.d(TAG, "复活广告视频开始播放")
                                }

                                override fun onVideoEnd() {
                                    Log.d(TAG, "复活广告视频播放完成")
                                }

                                override fun onReward() {
                                    Log.d(TAG, "复活广告奖励触发，角色复活")
                                    callback.onReviveSuccess()
                                }

                                override fun onServiceResponse(i: Int) {
                                    Log.d(TAG, "复活广告服务端回调: $i")
                                }
                            })
                        } else {
                            Log.e(TAG, "复活广告无效")
                            callback.onAdShowFailed("复活广告无效")
                        }
                    }

                    override fun onLoadFail(errorCode: String?, errorMessage: String?) {
                        Log.e(TAG, "复活广告加载失败: $errorCode - $errorMessage")
                        callback.onAdLoadFailed("复活广告加载失败: $errorMessage")
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "复活广告异常", e)
            callback.onAdLoadFailed("复活广告异常: ${e.message}")
        }
    }

    /**
     * 预加载激励视频广告
     * @param activity 当前Activity
     */
    fun preloadRewardVideo(activity: Activity) {
        Log.d(TAG, "预加载激励视频广告")

        // 检查广告SDK是否已初始化
        val application = activity.application as MountainSurvivalApplication
        if (!application.isAdSDKInitialized()) {
            Log.w(TAG, "广告SDK未初始化，跳过预加载")
            return
        }

        try {
            OSETRewardVideo.getInstance()
                .setContext(activity)
                .setUserId("mountain_survival_user")
                .setPosId(MountainSurvivalApplication.REWARD_VIDEO_AD_ID)
                .startLoad()
        } catch (e: Exception) {
            Log.e(TAG, "预加载激励视频广告异常", e)
        }
    }
}
