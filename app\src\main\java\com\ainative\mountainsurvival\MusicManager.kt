package com.ainative.mountainsurvival

import android.content.Context
import android.content.SharedPreferences
import android.media.MediaPlayer
import android.util.Log

/**
 * 音乐管理器
 * 负责管理全局音乐播放状态和设置
 */
object MusicManager {
    
    private const val TAG = "MusicManager"
    private const val PREFS_NAME = "music_settings"
    private const val KEY_MUSIC_ENABLED = "music_enabled"
    
    private var mediaPlayer: MediaPlayer? = null
    private var isMusicEnabled = true
    private var isInitialized = false
    
    /**
     * 初始化音乐管理器
     * @param context 上下文
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            Log.d(TAG, "音乐管理器已经初始化")
            return
        }
        
        Log.d(TAG, "初始化音乐管理器")
        
        // 从SharedPreferences读取音乐设置
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        isMusicEnabled = prefs.getBoolean(KEY_MUSIC_ENABLED, true)
        
        Log.d(TAG, "音乐设置已加载: isMusicEnabled=$isMusicEnabled")
        isInitialized = true
    }
    
    /**
     * 开始播放背景音乐
     * @param context 上下文
     */
    fun startBackgroundMusic(context: Context) {
        try {
            if (mediaPlayer == null) {
                Log.d(TAG, "创建MediaPlayer实例")
                mediaPlayer = MediaPlayer.create(context, R.raw.winter)
                mediaPlayer?.isLooping = true
            }
            
            if (isMusicEnabled && mediaPlayer?.isPlaying != true) {
                mediaPlayer?.start()
                Log.d(TAG, "背景音乐开始播放")
            }
        } catch (e: Exception) {
            Log.e(TAG, "开始播放背景音乐失败", e)
        }
    }
    
    /**
     * 暂停背景音乐
     */
    fun pauseBackgroundMusic() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.pause()
                    Log.d(TAG, "背景音乐已暂停")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "暂停背景音乐失败", e)
        }
    }
    
    /**
     * 恢复背景音乐
     */
    fun resumeBackgroundMusic() {
        try {
            if (isMusicEnabled) {
                mediaPlayer?.let { player ->
                    if (!player.isPlaying) {
                        player.start()
                        Log.d(TAG, "背景音乐已恢复")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复背景音乐失败", e)
        }
    }
    
    /**
     * 停止并释放背景音乐资源
     */
    fun stopBackgroundMusic() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
                Log.d(TAG, "背景音乐已停止并释放资源")
            }
            mediaPlayer = null
        } catch (e: Exception) {
            Log.e(TAG, "停止背景音乐失败", e)
        }
    }
    
    /**
     * 切换音乐开关状态
     * @param context 上下文，用于保存设置
     * @return 新的音乐状态
     */
    fun toggleMusic(context: Context): Boolean {
        isMusicEnabled = !isMusicEnabled
        
        // 保存设置到SharedPreferences
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_MUSIC_ENABLED, isMusicEnabled).apply()
        
        if (isMusicEnabled) {
            resumeBackgroundMusic()
            Log.d(TAG, "音乐已开启")
        } else {
            pauseBackgroundMusic()
            Log.d(TAG, "音乐已关闭")
        }
        
        return isMusicEnabled
    }
    
    /**
     * 获取当前音乐开关状态
     * @return true表示音乐开启，false表示音乐关闭
     */
    fun isMusicEnabled(): Boolean {
        return isMusicEnabled
    }
    
    /**
     * 获取音乐状态文本
     * @return 音乐状态的显示文本
     */
    fun getMusicStatusText(): String {
        return if (isMusicEnabled) "音乐：开" else "音乐：关"
    }
    
    /**
     * 检查音乐是否正在播放
     * @return true表示正在播放，false表示未播放
     */
    fun isPlaying(): Boolean {
        return try {
            mediaPlayer?.isPlaying ?: false
        } catch (e: Exception) {
            Log.e(TAG, "检查播放状态失败", e)
            false
        }
    }
}
