package com.ainative.mountainsurvival

/**
 * 游戏事件数据类
 * 用于表示游戏中的各种事件，与JSON数据结构对应
 */
data class GameEvent(
    /**
     * 事件唯一标识符
     * 例如: "day1_start", "day2_explore", "found_cans"
     */
    val id: String,

    /**
     * 事件描述文本
     * 显示给玩家的故事内容
     */
    val text: String,

    /**
     * 玩家可选择的选项列表
     * 每个选项包含文本、效果和可能的后续事件
     * 对于只有随机选择或条件事件的情况，此字段可以为空
     */
    val choices: List<Choice> = emptyList(),

    /**
     * 事件的直接效果（可选）
     * 某些事件在显示时就会产生效果，不需要玩家选择
     */
    val effects: Map<String, Any>? = null,

    // TODO: 随机事件功能暂时禁用，保留代码以备后续启用
    /*
    /**
     * 随机选择列表（可选）
     * 用于实现随机事件，系统会根据概率自动选择下一个事件
     */
    val randomChoices: List<RandomChoice>? = null,
    */

    /**
     * 结果文本（可选）
     * 在应用效果后显示的反馈文本
     */
    val resultText: String? = null,

    /**
     * 条件事件（可选）
     * 根据游戏状态条件选择不同的后续事件
     */
    val conditionalEvents: Map<String, String>? = null
)

/**
 * 玩家选择数据类
 * 表示玩家在事件中可以做出的选择
 */
data class Choice(
    /**
     * 选择的显示文本
     * 显示在按钮上的文字
     */
    val text: String,

    /**
     * 选择产生的效果
     * 键为属性名（如"warmth", "stamina"），值为变化量（正数增加，负数减少）
     */
    val effects: Map<String, Any>,

    /**
     * 下一个事件ID（可选）
     * 选择此选项后要触发的事件ID，如果为null则继续正常流程
     */
    val nextEventId: String? = null,

    /**
     * 结果文本（可选）
     * 选择此选项后显示的反馈文本
     */
    val resultText: String? = null,

    /**
     * 选择条件（可选）
     * 显示此选择需要满足的条件，例如需要特定物品或状态值
     */
    val requirements: Map<String, Any>? = null,

    /**
     * 特殊物品奖励（可选）
     * 选择此选项后获得的特殊物品
     */
    val specialItemReward: String? = null,

    /**
     * 获得的特殊物品（可选）
     * 与specialItemReward类似，用于JSON兼容性
     */
    val specialItemGained: String? = null,

    /**
     * 使用的特殊物品（可选）
     * 选择此选项后消耗的特殊物品
     */
    val specialItemUsed: String? = null,

    /**
     * 特殊效果（可选）
     * 用于设置特殊状态，如屋顶漏雪等
     */
    val specialEffects: Map<String, Any>? = null
)

/**
 * 随机选择数据类
 * 用于实现基于概率的随机事件
 */
data class RandomChoice(
    /**
     * 触发概率
     * 0.0 到 1.0 之间的值，表示触发此选择的概率
     */
    val probability: Double,

    /**
     * 下一个事件ID
     * 当此随机选择被触发时，要跳转到的事件ID
     */
    val nextEventId: String,

    /**
     * 随机选择的效果（可选）
     * 当此随机选择被触发时产生的效果
     */
    val effects: Map<String, Any>? = null,

    /**
     * 结果文本（可选）
     * 当此随机选择被触发时显示的文本
     */
    val resultText: String? = null
)

/**
 * 事件容器数据类
 * 用于解析包含多个事件的JSON文件
 */
data class EventContainer(
    /**
     * 事件列表
     * 包含游戏中所有的事件数据
     */
    val events: List<GameEvent>
)

/**
 * 游戏事件工具类
 * 提供事件处理的辅助方法
 */
object GameEventUtils {

    /**
     * 检查选择是否满足要求
     * @param choice 要检查的选择
     * @param gameState 当前游戏状态
     * @return 是否满足要求
     */
    fun isChoiceAvailable(choice: Choice, gameState: GameState): Boolean {
        choice.requirements?.forEach { (key, value) ->
            when (key) {
                "warmth" -> {
                    val required = convertToInt(value)
                    if (gameState.warmth < required) {
                        android.util.Log.d("GameEventUtils", "选择 '${choice.text}' 不可用: 体温不足 (需要: $required, 当前: ${gameState.warmth})")
                        return false
                    }
                }
                "stamina" -> {
                    val required = convertToInt(value)
                    if (gameState.stamina < required) {
                        android.util.Log.d("GameEventUtils", "选择 '${choice.text}' 不可用: 体力不足 (需要: $required, 当前: ${gameState.stamina})")
                        return false
                    }
                }
                "firewood" -> {
                    val required = convertToInt(value)
                    if (gameState.firewood < required) {
                        android.util.Log.d("GameEventUtils", "选择 '${choice.text}' 不可用: 木柴不足 (需要: $required, 当前: ${gameState.firewood})")
                        return false
                    }
                }
                "food" -> {
                    val required = convertToInt(value)
                    if (gameState.food < required) {
                        android.util.Log.d("GameEventUtils", "选择 '${choice.text}' 不可用: 食物不足 (需要: $required, 当前: ${gameState.food})")
                        return false
                    }
                }
                "special_item" -> if (!gameState.specialItems.contains(value as String)) return false
                "special_items", "specialItems" -> {
                    // 支持特殊物品列表要求（支持两种命名格式）
                    val requiredItems = when (value) {
                        is List<*> -> value.filterIsInstance<String>()
                        is String -> listOf(value)
                        else -> emptyList()
                    }

                    requiredItems.forEach { item ->
                        if (!gameState.specialItems.contains(item)) {
                            android.util.Log.d("GameEventUtils", "选择 '${choice.text}' 不可用: 缺少特殊物品 $item")
                            return false
                        }
                    }
                }
                "min_day" -> if (gameState.currentDay < convertToInt(value)) return false
                "max_day" -> if (gameState.currentDay > convertToInt(value)) return false
                // 特殊状态检查
                "hasTools", "hasTrap", "hasBackupShelter", "knowsBackupShelter",
                "inBackupShelter", "hasSurvivalKnowledge" -> {
                    val required = value as Boolean
                    val current = gameState.hasSpecialState(key)
                    if (required != current) {
                        android.util.Log.d("GameEventUtils", "选择 '${choice.text}' 不可用: 特殊状态 $key 不匹配 (需要: $required, 当前: $current)")
                        return false
                    }
                }
                else -> {
                    android.util.Log.w("GameEventUtils", "未知的要求类型: $key")
                }
            }
        }
        return true
    }

    /**
     * 安全地将Any类型转换为Int
     * 处理JSON解析时可能出现的Double类型
     */
    private fun convertToInt(value: Any): Int {
        return when (value) {
            is Int -> value
            is Double -> value.toInt()
            is Float -> value.toInt()
            is Long -> value.toInt()
            is String -> value.toIntOrNull() ?: 0
            else -> 0
        }
    }

    // TODO: 随机事件功能暂时禁用，保留代码以备后续启用
    /*
    /**
     * 根据概率选择随机事件
     * @param randomChoices 随机选择列表
     * @return 选中的随机选择，如果没有选中则返回null
     */
    fun selectRandomEvent(randomChoices: List<RandomChoice>): RandomChoice? {
        val random = Math.random()
        var cumulativeChance = 0.0

        for (choice in randomChoices) {
            cumulativeChance += choice.probability
            if (random <= cumulativeChance) {
                return choice
            }
        }

        return null
    }
    */

    /**
     * 验证事件数据的完整性
     * @param event 要验证的事件
     * @return 验证结果，true表示数据有效
     */
    fun validateEvent(event: GameEvent): Boolean {
        // 检查基本字段
        if (event.id.isBlank() || event.text.isBlank()) {
            return false
        }

        // 检查选择列表
        // TODO: 随机事件检查暂时禁用
        if (event.choices.isEmpty() /* && event.randomChoices.isNullOrEmpty() */) {
            return false
        }

        // 检查每个选择的有效性
        event.choices.forEach { choice ->
            if (choice.text.isBlank()) {
                return false
            }
        }

        return true
    }
}
