package com.ainative.mountainsurvival

import android.media.MediaPlayer
import android.os.Bundle
import android.util.Log
import android.view.View
import android.support.v7.app.AlertDialog
import android.support.v7.app.AppCompatActivity
import com.ainative.mountainsurvival.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private var eventMap: Map<String, GameEvent> = emptyMap()
    private var currentEvent: GameEvent? = null
    private var currentChoices: List<Choice> = emptyList()



    // 新增：游戏状态管理
    private var gameUIState: GameUIState = GameUIState.NORMAL_CHOICE
    private var pendingChoice: Choice? = null
    private var pendingNightEventId: String? = null

    // 广告复活相关
    private var savedGameState: GameState? = null
    private var savedCurrentEvent: GameEvent? = null
    private var savedCurrentChoices: List<Choice> = emptyList()
    private var savedGameUIState: GameUIState = GameUIState.NORMAL_CHOICE

    // 自动备份状态（在每次状态变化时保存）
    private var lastValidGameState: GameState? = null
    private var lastValidCurrentEvent: GameEvent? = null
    private var lastValidCurrentChoices: List<Choice> = emptyList()
    private var lastValidGameUIState: GameUIState = GameUIState.NORMAL_CHOICE

    // 动态规划状态转换表
    private val stateTransitionDP = mutableMapOf<Pair<GameUIState, String>, GameUIState>()

    companion object {
        private const val TAG = "MainActivity"
    }

    /**
     * 游戏UI状态枚举
     * 使用动态规划方法管理状态转换
     */
    enum class GameUIState {
        NORMAL_CHOICE,          // 正常选择状态
        WAITING_CONTINUE,       // 等待继续状态（选择结果后）
        FOOD_CHOICE,            // 夜晚前食物选择状态
        FOOD_WAITING_CONTINUE,  // 食物选择后等待继续状态
        NIGHT_PHASE,            // 夜晚阶段
        NIGHT_WAITING_CONTINUE, // 夜晚等待继续状态
        GAME_OVER              // 游戏结束
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置 View Binding
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root as View)

        // 初始化动态规划状态转换表
        initializeStateTransitionDP()

        // 初始化 GameManager
        GameManager.initializeGame()
        Log.d(TAG, "GameManager 初始化完成")

        // 初始化 GameEngine 并加载事件数据
        eventMap = GameEngine.loadEvents(this)

        if (eventMap.isNotEmpty()) {
            // 验证事件数据
            val issues = GameEngine.validateEventMap(eventMap)
            if (issues.isNotEmpty()) {
                Log.w(TAG, "发现事件验证问题:")
                issues.forEach { issue ->
                    Log.w(TAG, "- $issue")
                }
            } else {
                Log.d(TAG, "事件数据验证通过")
            }

            // 显示统计信息
            val stats = GameEngine.getEventMapStatistics(eventMap)
            Log.d(TAG, "事件统计: $stats")

            // 运行游戏循环测试（可选，用于调试）
            val gameLoopTestResult = GameLoopTest.testGameLoop()
            Log.d(TAG, "游戏循环测试结果: 成功=${gameLoopTestResult.success}")
            if (gameLoopTestResult.issues.isNotEmpty()) {
                gameLoopTestResult.issues.forEach { issue ->
                    Log.w(TAG, "游戏循环问题: $issue")
                }
            }

            // 重新初始化游戏（因为测试可能改变了状态）
            GameManager.initializeGame()

            // 设置按钮点击监听器
            setupClickListeners()

            // 初始化音乐管理器并继续播放背景音乐
            MusicManager.initialize(this)
            MusicManager.startBackgroundMusic(this)

            // 开始游戏 - 显示第一个事件
            displayEvent("day1_start")
        } else {
            // 事件加载失败的处理
            Log.e(TAG, "事件加载失败")
            binding.storyTextView.text = "游戏数据加载失败，请重新启动应用。"
        }
    }

    /**
     * 初始化动态规划状态转换表
     * 使用动态规划方法预计算所有可能的状态转换
     */
    private fun initializeStateTransitionDP() {
        Log.d(TAG, "初始化状态转换动态规划表...")

        // 定义状态转换规则
        val transitions = listOf(
            // 从正常选择状态的转换
            Triple(GameUIState.NORMAL_CHOICE, "choice_made", GameUIState.WAITING_CONTINUE),
            Triple(GameUIState.NORMAL_CHOICE, "game_over", GameUIState.GAME_OVER),

            // 从等待继续状态的转换
            Triple(GameUIState.WAITING_CONTINUE, "continue_clicked", GameUIState.NORMAL_CHOICE),
            Triple(GameUIState.WAITING_CONTINUE, "food_phase", GameUIState.FOOD_CHOICE),
            Triple(GameUIState.WAITING_CONTINUE, "night_phase", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.WAITING_CONTINUE, "game_over", GameUIState.GAME_OVER),

            // 从食物选择状态的转换
            Triple(GameUIState.FOOD_CHOICE, "food_choice_made", GameUIState.FOOD_WAITING_CONTINUE),
            Triple(GameUIState.FOOD_CHOICE, "night_phase", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.FOOD_CHOICE, "game_over", GameUIState.GAME_OVER),

            // 从食物等待继续状态的转换
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "food_continue_eating", GameUIState.FOOD_CHOICE),
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "food_continue_night", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "night_phase", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "game_over", GameUIState.GAME_OVER),

            // 从夜晚阶段的转换
            Triple(GameUIState.NIGHT_PHASE, "night_shown", GameUIState.NIGHT_WAITING_CONTINUE),
            Triple(GameUIState.NIGHT_PHASE, "night_continue_clicked", GameUIState.NORMAL_CHOICE),
            Triple(GameUIState.NIGHT_PHASE, "game_over", GameUIState.GAME_OVER),

            // 从夜晚等待继续状态的转换
            Triple(GameUIState.NIGHT_WAITING_CONTINUE, "night_continue_clicked", GameUIState.NORMAL_CHOICE),
            Triple(GameUIState.NIGHT_WAITING_CONTINUE, "game_over", GameUIState.GAME_OVER),

            // 游戏结束状态（终态）
            Triple(GameUIState.GAME_OVER, "restart", GameUIState.NORMAL_CHOICE)
        )

        // 使用动态规划填充状态转换表
        transitions.forEach { (fromState, action, toState) ->
            stateTransitionDP[Pair(fromState, action)] = toState
        }

        Log.d(TAG, "状态转换表初始化完成，共 ${stateTransitionDP.size} 个转换规则")
    }

    /**
     * 使用动态规划进行状态转换
     * @param action 触发的动作
     * @return 是否成功转换状态
     */
    private fun transitionState(action: String): Boolean {
        val newState = stateTransitionDP[Pair(gameUIState, action)]

        return if (newState != null) {
            val oldState = gameUIState
            gameUIState = newState
            Log.d(TAG, "状态转换: $oldState -> $newState (动作: $action)")
            true
        } else {
            Log.w(TAG, "无效的状态转换: $gameUIState + $action")
            false
        }
    }

    /**
     * 更新UI状态显示
     * 专门负责读取 GameManager.gameState 的当前值，并将它们更新到顶部的四个 TextView 上
     */
    private fun updateUI() {
        val gameState = GameManager.gameState

        // 更新状态显示
        binding.warmthTextView.text = gameState.getStatusText("warmth")
        binding.staminaTextView.text = gameState.getStatusText("stamina")
        binding.firewoodTextView.text = gameState.getStatusText("firewood")
        binding.foodTextView.text = gameState.getStatusText("food")

        Log.d(TAG, "UI状态更新: 体温=${gameState.warmth}, 体力=${gameState.stamina}, 木柴=${gameState.firewood}, 食物=${gameState.food}")
    }

    /**
     * 显示事件
     * 根据ID从 GameEngine 获取事件，然后将事件文本和选项文本设置到对应的 TextView 和 Button 上
     *
     * @param eventId 事件ID
     */
    private fun displayEvent(eventId: String) {
        Log.d(TAG, "显示事件: $eventId，当前UI状态: $gameUIState")



        // 确保状态为正常选择状态，但保留夜晚阶段状态
        if (gameUIState != GameUIState.NORMAL_CHOICE && gameUIState != GameUIState.NIGHT_PHASE) {
            gameUIState = GameUIState.NORMAL_CHOICE
            Log.d(TAG, "重置UI状态为正常选择，准备显示新事件")
        } else if (gameUIState == GameUIState.NIGHT_PHASE) {
            Log.d(TAG, "保持夜晚阶段状态，不重置UI状态")
        }

        // 从 GameEngine 获取事件
        currentEvent = GameEngine.getEvent(eventMap, eventId)

        if (currentEvent == null) {
            Log.e(TAG, "事件不存在: $eventId")
            binding.storyTextView.text = "错误：找不到事件 $eventId"
            return
        }

        val event = currentEvent!!

        // 特殊处理：如果是夜晚事件，执行夜晚结算
        if (isNightEvent(eventId)) {
            Log.d(TAG, "检测到夜晚事件，执行夜晚结算: $eventId")
            val nightResult = GameManager.performNightPhase()

            // 生成夜晚结算文本
            val nightSettlementText = generateNightSettlementText(nightResult)

            // 组合事件文本和结算文本
            val combinedText = "${event.text}\n\n$nightSettlementText"
            binding.storyTextView.text = combinedText

            // 更新UI显示
            updateUI()

            // 备份当前有效状态（在检查游戏结束之前）
            backupValidGameState()

            // 检查游戏是否结束（夜晚结算后可能导致死亡）
            if (GameManager.checkGameOver()) {
                Log.d(TAG, "夜晚结算后游戏结束")
                // 立即显示游戏结束界面
                showGameOverDialog()
                return
            }
        } else {
            // 应用事件的直接效果（如果有）
            var choiceResult: GameManager.ChoiceResult? = null
            event.effects?.let { effects ->
                Log.d(TAG, "应用事件直接效果: $effects")
                val tempChoice = Choice(
                    text = "事件效果",
                    effects = effects
                )
                choiceResult = GameManager.applyChoice(tempChoice)
            }

            // 设置事件文本，如果有状态变化则添加状态变化信息
            val eventText = if (choiceResult != null && choiceResult!!.stateChanges.isNotEmpty()) {
                val stateChangesText = generateStateChangesText(choiceResult!!.stateChanges)
                if (stateChangesText.isNotEmpty()) {
                    "${event.text}\n\n$stateChangesText"
                } else {
                    event.text
                }
            } else {
                event.text
            }

            binding.storyTextView.text = eventText
        }
        Log.d(TAG, "=== 事件文本已更新 ===")
        Log.d(TAG, "事件ID: ${event.id}")
        Log.d(TAG, "事件文本: ${event.text.take(100)}...")
        Log.d(TAG, "事件选择数: ${event.choices.size}")

        // 处理条件事件
        event.conditionalEvents?.let { conditionalEvents ->
            val selectedEventId = evaluateConditionalEvent(conditionalEvents)
            if (selectedEventId != null) {
                Log.d(TAG, "触发条件事件: $selectedEventId")

                // 立即显示条件事件，不延迟
                displayEvent(selectedEventId)
                return
            }
        }

        // TODO: 随机事件功能暂时禁用，保留代码以备后续启用
        /*
        // 处理随机事件
        event.randomChoices?.let { randomChoices ->
            Log.d(TAG, "检测到随机事件，选择数量: ${randomChoices.size}")
            val selectedChoice = GameEventUtils.selectRandomEvent(randomChoices)
            if (selectedChoice != null) {
                Log.d(TAG, "触发随机事件: ${selectedChoice.nextEventId}")

                // 应用随机选择的效果（如果有）
                var choiceResult: GameManager.ChoiceResult? = null
                selectedChoice.effects?.let { effects ->
                    Log.d(TAG, "应用随机事件效果: $effects")
                    val tempChoice = Choice(
                        text = "随机事件效果",
                        effects = effects
                    )
                    choiceResult = GameManager.applyChoice(tempChoice)
                } ?: run {
                    Log.d(TAG, "随机事件没有effects")
                }

                // 设置随机事件文本，如果有结果文本则显示，否则显示原事件文本
                val randomEventText = selectedChoice.resultText ?: event.text

                // 如果有状态变化，添加状态变化信息
                val finalText = if (choiceResult != null && choiceResult!!.stateChanges.isNotEmpty()) {
                    val stateChangesText = generateStateChangesText(choiceResult!!.stateChanges)
                    if (stateChangesText.isNotEmpty()) {
                        "$randomEventText\n\n$stateChangesText"
                    } else {
                        randomEventText
                    }
                } else {
                    randomEventText
                }

                binding.storyTextView.text = finalText

                // 更新UI显示
                updateUI()

                // 检查游戏是否结束
                if (GameManager.checkGameOver()) {
                    Log.d(TAG, "随机事件导致游戏结束")
                    // 立即显示游戏结束界面
                    showGameOverDialog()
                    return
                }

                // 显示继续按钮，让用户点击后再处理下一个事件
                showContinueButton()

                // 设置pendingChoice指向实际的随机事件，确保用户能看到完整的事件内容
                pendingChoice = Choice(
                    text = "随机事件继续",
                    effects = emptyMap(),
                    nextEventId = selectedChoice.nextEventId
                )
                Log.d(TAG, "设置pendingChoice用于随机事件: ${selectedChoice.nextEventId}")
                return
            } else {
                Log.d(TAG, "随机事件选择失败，selectedChoice为null")
            }
        } ?: run {
            Log.d(TAG, "事件没有随机选择")
        }
        */



        // 获取可用的选择（根据游戏状态过滤）
        currentChoices = getAvailableChoices(event)



        // 特殊处理：如果没有可用选择且没有条件事件，创建默认选择
        if (currentChoices.isEmpty() && event.conditionalEvents.isNullOrEmpty()) {
            // 对于没有选择的事件，创建默认选择
            Log.w(TAG, "事件 ${event.id} 没有可用选择，创建默认选择")
            currentChoices = listOf(
                Choice(
                    text = "继续",
                    effects = emptyMap(),
                    resultText = "你别无选择，只能继续前进..."
                )
            )
        }

        // 设置选项按钮（只有当有选择时才设置）
        if (currentChoices.isNotEmpty()) {
            Log.d(TAG, "设置 ${currentChoices.size} 个选择按钮")
            setupChoiceButtons(currentChoices)
        } else {
            // 对于没有选择的事件（如条件事件），隐藏所有按钮
            // TODO: 随机事件相关注释已更新
            binding.choice1Button.visibility = View.GONE
            binding.choice2Button.visibility = View.GONE
            binding.choice3Button.visibility = View.GONE
            binding.choice4Button.visibility = View.GONE
            Log.d(TAG, "事件 ${event.id} 没有选择，隐藏所有按钮")
        }

        // 更新UI状态显示
        updateUI()

        // 备份当前有效状态
        backupValidGameState()

        Log.d(TAG, "事件显示完成，可用选择数: ${currentChoices.size}")
    }

    /**
     * 获取可用的选择
     * 根据游戏状态过滤出玩家可以选择的选项
     */
    private fun getAvailableChoices(event: GameEvent): List<Choice> {
        val gameState = GameManager.gameState
        Log.d(TAG, "检查事件 ${event.id} 的可用选择，总选择数: ${event.choices.size}")
        Log.d(TAG, "当前游戏状态: 体温=${gameState.warmth}, 体力=${gameState.stamina}, 木柴=${gameState.firewood}, 食物=${gameState.food}")


        val availableChoices = event.choices.filter { choice ->
            val isAvailable = GameEventUtils.isChoiceAvailable(choice, gameState)
            Log.d(TAG, "选择 '${choice.text}' 是否可用: $isAvailable, 要求: ${choice.requirements}")

            // 详细记录不可用的原因
            if (!isAvailable && choice.requirements != null) {
                choice.requirements.forEach { (key, value) ->
                    when (key) {
                        "warmth" -> if (gameState.warmth < convertToInt(value)) {
                            Log.d(TAG, "  - 体温不足: 需要${value}, 当前${gameState.warmth}")
                        }
                        "stamina" -> if (gameState.stamina < convertToInt(value)) {
                            Log.d(TAG, "  - 体力不足: 需要${value}, 当前${gameState.stamina}")
                        }
                        "firewood" -> if (gameState.firewood < convertToInt(value)) {
                            Log.d(TAG, "  - 木柴不足: 需要${value}, 当前${gameState.firewood}")
                        }
                        "food" -> if (gameState.food < convertToInt(value)) {
                            Log.d(TAG, "  - 食物不足: 需要${value}, 当前${gameState.food}")
                        }
                    }
                }
            }

            isAvailable
        }

        Log.d(TAG, "可用选择数: ${availableChoices.size}")
        if (availableChoices.isNotEmpty()) {
            availableChoices.forEachIndexed { index, choice ->
                Log.d(TAG, "可用选择 $index: ${choice.text}")
            }
        }

        return availableChoices
    }

    /**
     * 安全地将Any类型转换为Int
     * 处理JSON解析时可能出现的Double类型
     */
    private fun convertToInt(value: Any): Int {
        return when (value) {
            is Int -> value
            is Double -> value.toInt()
            is Float -> value.toInt()
            is Long -> value.toInt()
            is String -> value.toIntOrNull() ?: 0
            else -> 0
        }
    }

    /**
     * 设置选择按钮
     * 根据可用选择和当前UI状态设置按钮的文本和可见性
     */
    private fun setupChoiceButtons(choices: List<Choice>) {
        Log.d(TAG, "设置选择按钮，当前状态: $gameUIState，选择数量: ${choices.size}")

        // 确保按钮监听器已设置（防止复活后按钮无法点击）
        if (!areButtonListenersSet()) {
            Log.w(TAG, "检测到按钮监听器未设置，重新设置")
            setupClickListeners()
        }

        // 隐藏所有按钮
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE
        binding.choice4Button.visibility = View.GONE

        when (gameUIState) {
            GameUIState.NORMAL_CHOICE -> {
                // 正常选择状态：显示选择选项
                if (choices.isEmpty()) {
                    Log.w(TAG, "正常选择状态但没有可用选择")
                    return
                }

                choices.forEachIndexed { index, choice ->
                    when (index) {
                        0 -> {
                            binding.choice1Button.visibility = View.VISIBLE
                            binding.choice1Button.text = choice.text
                            binding.choice1Button.isEnabled = true
                            Log.d(TAG, "设置选择1: ${choice.text}")
                        }
                        1 -> {
                            binding.choice2Button.visibility = View.VISIBLE
                            binding.choice2Button.text = choice.text
                            binding.choice2Button.isEnabled = true
                            Log.d(TAG, "设置选择2: ${choice.text}")
                        }
                        2 -> {
                            binding.choice3Button.visibility = View.VISIBLE
                            binding.choice3Button.text = choice.text
                            binding.choice3Button.isEnabled = true
                            Log.d(TAG, "设置选择3: ${choice.text}")
                        }
                        3 -> {
                            binding.choice4Button.visibility = View.VISIBLE
                            binding.choice4Button.text = choice.text
                            binding.choice4Button.isEnabled = true
                            Log.d(TAG, "设置选择4: ${choice.text}")
                        }
                    }
                }
            }
            GameUIState.WAITING_CONTINUE -> {
                // 等待继续状态：只显示"继续"按钮
                binding.choice1Button.visibility = View.VISIBLE
                binding.choice1Button.text = "继续"
                Log.d(TAG, "设置继续按钮")
            }
            GameUIState.FOOD_CHOICE -> {
                // 食物选择状态：显示食物相关选择
                choices.forEachIndexed { index, choice ->
                    when (index) {
                        0 -> {
                            binding.choice1Button.visibility = View.VISIBLE
                            binding.choice1Button.text = choice.text
                            Log.d(TAG, "设置食物选择1: ${choice.text}")
                        }
                        1 -> {
                            binding.choice2Button.visibility = View.VISIBLE
                            binding.choice2Button.text = choice.text
                            Log.d(TAG, "设置食物选择2: ${choice.text}")
                        }
                    }
                }
            }
            GameUIState.FOOD_WAITING_CONTINUE -> {
                // 食物等待继续状态：只显示"继续"按钮
                binding.choice1Button.visibility = View.VISIBLE
                binding.choice1Button.text = "继续"
                Log.d(TAG, "设置食物继续按钮")
            }
            GameUIState.NIGHT_PHASE -> {
                // 夜晚阶段：如果有选择则显示，否则隐藏按钮
                if (choices.isNotEmpty()) {
                    Log.d(TAG, "夜晚阶段有选择，显示按钮")
                    // 显示选择按钮（与正常选择相同的逻辑）
                    for (i in choices.indices) {
                        val button = when (i) {
                            0 -> binding.choice1Button
                            1 -> binding.choice2Button
                            2 -> binding.choice3Button
                            else -> continue
                        }
                        button.visibility = View.VISIBLE
                        button.text = choices[i].text
                        Log.d(TAG, "设置夜晚选择${i + 1}: ${choices[i].text}")
                    }
                } else {
                    Log.d(TAG, "夜晚阶段无选择，隐藏所有按钮")
                }
            }
            GameUIState.NIGHT_WAITING_CONTINUE -> {
                // 夜晚等待继续状态：只显示"继续"按钮
                binding.choice1Button.visibility = View.VISIBLE
                binding.choice1Button.text = "继续"
                Log.d(TAG, "设置夜晚继续按钮")
            }
            GameUIState.GAME_OVER -> {
                // 游戏结束：按钮由对话框处理
                Log.d(TAG, "游戏结束状态，按钮由对话框处理")
            }
        }
    }

    /**
     * 显示继续按钮
     * 当显示选择结果后调用此方法
     */
    private fun showContinueButton() {
        if (transitionState("choice_made")) {
            setupChoiceButtons(emptyList()) // 传入空列表，因为此时不需要选择按钮
        }
    }

    /**
     * 设置按钮点击监听器
     * 为选项按钮和广告按钮设置 setOnClickListener
     */
    private fun setupClickListeners() {
        Log.d(TAG, "设置按钮点击监听器")

        // 选择按钮1
        binding.choice1Button.setOnClickListener {
            Log.d(TAG, "选择按钮1被点击")
            handleChoiceClick(0)
        }

        // 选择按钮2
        binding.choice2Button.setOnClickListener {
            Log.d(TAG, "选择按钮2被点击")
            handleChoiceClick(1)
        }

        // 选择按钮3
        binding.choice3Button.setOnClickListener {
            Log.d(TAG, "选择按钮3被点击")
            handleChoiceClick(2)
        }

        // 选择按钮4
        binding.choice4Button.setOnClickListener {
            Log.d(TAG, "选择按钮4被点击")
            handleChoiceClick(3)
        }

        // 广告按钮点击事件
        binding.warmthPlusButton.setOnClickListener {
            showAdForResource(AdManager.ResourceType.WARMTH)
        }

        binding.staminaPlusButton.setOnClickListener {
            showAdForResource(AdManager.ResourceType.STAMINA)
        }

        binding.firewoodPlusButton.setOnClickListener {
            showAdForResource(AdManager.ResourceType.FIREWOOD)
        }

        binding.foodPlusButton.setOnClickListener {
            showAdForResource(AdManager.ResourceType.FOOD)
        }

        Log.d(TAG, "按钮点击监听器设置完成")
    }

    /**
     * 检查按钮监听器是否已设置
     * 通过检查按钮是否有OnClickListener来判断
     */
    private fun areButtonListenersSet(): Boolean {
        return binding.choice1Button.hasOnClickListeners() &&
               binding.choice2Button.hasOnClickListeners() &&
               binding.choice3Button.hasOnClickListeners() &&
               binding.choice4Button.hasOnClickListeners()
    }

    /**
     * 处理选择按钮点击
     * 根据当前UI状态处理不同的点击逻辑
     *
     * @param choiceIndex 选择的索引（0, 1, 2）
     */
    private fun handleChoiceClick(choiceIndex: Int) {
        Log.d(TAG, "处理选择按钮点击: 索引=$choiceIndex, 当前状态=$gameUIState, 可用选择数=${currentChoices.size}")

        when (gameUIState) {
            GameUIState.NORMAL_CHOICE -> {
                if (choiceIndex < currentChoices.size) {
                    handleNormalChoice(choiceIndex)
                } else {
                    Log.w(TAG, "选择索引超出范围: $choiceIndex >= ${currentChoices.size}")
                }
            }
            GameUIState.WAITING_CONTINUE -> {
                Log.d(TAG, "等待继续状态，处理继续点击")
                handleContinueClick()
            }
            GameUIState.FOOD_CHOICE -> {
                Log.d(TAG, "食物选择状态，处理食物选择")
                handleFoodChoice(choiceIndex)
            }
            GameUIState.FOOD_WAITING_CONTINUE -> {
                Log.d(TAG, "食物等待继续状态，处理食物继续点击")
                handleFoodContinueClick()
            }
            GameUIState.NIGHT_PHASE -> {
                // 夜晚阶段点击按钮，直接进入下一天
                Log.d(TAG, "夜晚阶段按钮点击，直接进入下一天")
                handleNightContinueClick()
            }
            GameUIState.NIGHT_WAITING_CONTINUE -> {
                Log.d(TAG, "夜晚等待继续状态，处理夜晚继续点击")
                handleNightContinueClick()
            }
            GameUIState.GAME_OVER -> {
                Log.w(TAG, "游戏结束状态，按钮点击应该由对话框处理")
            }
            else -> {
                Log.w(TAG, "当前状态 $gameUIState 不支持按钮点击")
            }
        }
    }

    /**
     * 处理正常选择点击
     * @param choiceIndex 选择的索引
     */
    private fun handleNormalChoice(choiceIndex: Int) {
        if (choiceIndex >= currentChoices.size) {
            Log.w(TAG, "无效的选择索引: $choiceIndex, 可用选择数: ${currentChoices.size}")
            return
        }

        // a. 获取当前事件对应的 Choice 对象
        val choice = currentChoices[choiceIndex]
        Log.d(TAG, "玩家选择: ${choice.text}")

        // b. 调用 GameManager.applyChoice() 来处理数值变化
        val choiceResult = GameManager.applyChoice(choice)

        if (!choiceResult.success) {
            Log.e(TAG, "选择应用失败: ${choiceResult.message}")
            return
        }

        Log.d(TAG, "选择应用成功: ${choiceResult.message}")

        // 显示状态变化信息
        choiceResult.stateChanges.forEach { (property, change) ->
            val (oldValue, newValue) = change
            val diff = newValue - oldValue
            val sign = if (diff >= 0) "+" else ""
            Log.d(TAG, "状态变化 - $property: $oldValue -> $newValue ($sign$diff)")
        }

        // 显示特殊物品获得信息
        choiceResult.specialItemGained?.let { item ->
            Log.d(TAG, "获得特殊物品: $item")
            // 这里可以添加UI提示
        }

        // 更新UI显示
        updateUI()

        // 备份当前有效状态（在检查游戏结束之前）
        backupValidGameState()

        // 检查游戏是否结束（选择效果可能导致死亡）
        if (GameManager.checkGameOver()) {
            Log.d(TAG, "选择效果导致游戏结束")
            // 立即显示游戏结束界面
            showGameOverDialog()
            return
        }

        // 保存当前选择，用于继续时处理
        pendingChoice = choice

        // 显示选择结果文本（如果有）
        choice.resultText?.let { resultText ->
            // 生成状态变化文本
            val stateChangesText = generateStateChangesText(choiceResult.stateChanges)

            // 调试日志
            Log.d(TAG, "=== 状态变化调试 ===")
            Log.d(TAG, "选择: ${choice.text}")
            Log.d(TAG, "选择effects: ${choice.effects}")
            Log.d(TAG, "状态变化记录: ${choiceResult.stateChanges}")
            Log.d(TAG, "状态变化文本: '$stateChangesText'")

            // 组合结果文本和状态变化文本
            val fullResultText = if (stateChangesText.isNotEmpty()) {
                "$resultText\n\n$stateChangesText"
            } else {
                resultText
            }

            Log.d(TAG, "完整结果文本: '$fullResultText'")
            binding.storyTextView.text = fullResultText
            Log.d(TAG, "显示选择结果: ${resultText.take(50)}...")

            // 显示继续按钮，等待用户点击
            showContinueButton()
        } ?: run {
            // 如果没有结果文本，但有状态变化，显示状态变化
            val stateChangesText = generateStateChangesText(choiceResult.stateChanges)
            if (stateChangesText.isNotEmpty()) {
                binding.storyTextView.text = "你的选择产生了以下效果：\n\n$stateChangesText"
                showContinueButton()
            } else {
                // 如果既没有结果文本也没有状态变化，直接处理下一个事件
                processNextStep(choice)
            }
        }
    }

    /**
     * 处理继续按钮点击
     */
    private fun handleContinueClick() {
        Log.d(TAG, "用户点击继续按钮，当前状态: $gameUIState")

        pendingChoice?.let { choice ->
            Log.d(TAG, "处理待处理的选择: ${choice.text}, nextEventId: ${choice.nextEventId}")

            // 直接处理下一步，不需要状态转换（displayEvent会处理状态）
            processNextStep(choice)
        } ?: run {
            Log.w(TAG, "没有待处理的选择，这可能是一个错误状态")
            // 如果没有待处理的选择，强制回到正常状态
            gameUIState = GameUIState.NORMAL_CHOICE
            setupChoiceButtons(currentChoices)
        }
    }

    /**
     * 处理食物选择按钮点击
     */
    private fun handleFoodChoice(choiceIndex: Int) {
        Log.d(TAG, "用户选择食物选项: $choiceIndex")

        if (choiceIndex < currentChoices.size) {
            val choice = currentChoices[choiceIndex]
            Log.d(TAG, "处理食物选择: ${choice.text}")

            // 记录选择类型
            val isEatingChoice = choice.effects.containsKey("food") && choice.effects["food"] == -1

            Log.d(TAG, "=== 进食选择处理调试 ===")
            Log.d(TAG, "选择文本: ${choice.text}")
            Log.d(TAG, "选择effects: ${choice.effects}")
            Log.d(TAG, "是否为进食选择: $isEatingChoice")

            // 应用选择效果
            val choiceResult = GameManager.applyChoice(choice)
            val stateChanges = choiceResult.stateChanges

            // 显示选择结果
            val resultText = choice.resultText ?: "你做出了选择。"
            val stateChangesText = generateStateChangesText(stateChanges)
            val fullText = if (stateChangesText.isNotEmpty()) {
                "$resultText\n\n$stateChangesText"
            } else {
                resultText
            }

            binding.storyTextView.text = fullText

            // 更新UI
            updateUI()

            // 保存当前选择信息用于后续判断
            pendingChoice = choice

            // 转换到食物等待继续状态
            if (transitionState("food_choice_made")) {
                setupChoiceButtons(emptyList())
            }
        }
    }

    /**
     * 处理食物继续按钮点击
     */
    private fun handleFoodContinueClick() {
        Log.d(TAG, "用户点击食物继续按钮")

        // 检查上一次的选择是否是进食（在清除之前）
        val foodEffect = pendingChoice?.effects?.get("food")
        val wasEating = foodEffect == -1
        val currentFood = GameManager.gameState.food

        Log.d(TAG, "=== 进食继续点击调试 ===")
        Log.d(TAG, "pendingChoice: ${pendingChoice?.text}")
        Log.d(TAG, "pendingChoice effects: ${pendingChoice?.effects}")
        Log.d(TAG, "food effect value: $foodEffect")
        Log.d(TAG, "wasEating: $wasEating")
        Log.d(TAG, "currentFood: $currentFood")
        Log.d(TAG, "条件判断: wasEating($wasEating) && currentFood($currentFood) > 0 = ${wasEating && currentFood > 0}")

        // 清除待处理的选择
        pendingChoice = null

        if (wasEating && currentFood > 0) {
            // 如果上次选择了进食且还有食物，继续显示食物选择
            Log.d(TAG, "用户选择了进食且还有食物，继续显示食物选择")
            if (transitionState("food_continue_eating")) {
                showFoodChoicePhase()
            }
        } else {
            // 如果上次选择了不进食，或者没有食物了，进入夜晚阶段
            Log.d(TAG, "食物阶段结束（用户选择不进食或食物耗尽），进入夜晚阶段")

            // 使用新的状态转换进入夜晚阶段
            if (transitionState("food_continue_night")) {
                // 检查是否有待处理的夜晚事件
                val nightEventId = pendingNightEventId
                pendingNightEventId = null // 清除待处理的夜晚事件ID

                if (nightEventId != null && eventMap.containsKey(nightEventId)) {
                    Log.d(TAG, "显示待处理的夜晚事件: $nightEventId")
                    displayEvent(nightEventId)
                } else {
                    Log.d(TAG, "执行夜晚阶段")
                    performNightPhaseAndCheck()
                }
            } else {
                Log.e(TAG, "状态转换失败，强制执行夜晚阶段")
                forceNightPhase()
            }
        }
    }

    /**
     * 处理夜晚继续按钮点击
     */
    private fun handleNightContinueClick() {
        Log.d(TAG, "用户点击夜晚继续按钮")

        // 转换状态并继续游戏流程
        if (transitionState("night_continue_clicked")) {
            // 检查游戏是否结束
            if (GameManager.checkGameOver()) {
                transitionState("game_over")
                showGameOverDialog()
            } else {
                // 进入下一天
                proceedToNextDay()
            }
        }
    }

    /**
     * 生成状态变化文本
     * 只显示可见属性的数值变化，隐藏属性通过文字提示
     * @param stateChanges 状态变化映射
     * @return 格式化的状态变化文本
     */
    private fun generateStateChangesText(stateChanges: Map<String, Pair<Int, Int>>): String {
        Log.d(TAG, "=== generateStateChangesText 调试 ===")
        Log.d(TAG, "输入的stateChanges: $stateChanges")

        if (stateChanges.isEmpty()) {
            Log.d(TAG, "stateChanges为空，返回空字符串")
            return ""
        }

        // 只显示可见属性的数值变化
        val visiblePropertyDisplayNames = mapOf(
            "warmth" to "体温",
            "stamina" to "体力",
            "firewood" to "木柴",
            "food" to "食物"
        )

        val visibleChanges = mutableListOf<String>()
        val hiddenEffects = mutableListOf<String>()

        stateChanges.forEach { (property, change) ->
            val (oldValue, newValue) = change
            val diff = newValue - oldValue

            Log.d(TAG, "处理属性: $property, 旧值: $oldValue, 新值: $newValue, 差值: $diff")

            if (diff != 0) {
                when (property) {
                    in visiblePropertyDisplayNames.keys -> {
                        // 显示可见属性的具体数值变化
                        val displayName = visiblePropertyDisplayNames[property]!!
                        val sign = if (diff > 0) "+" else ""
                        val changeText = "$displayName$sign$diff"
                        visibleChanges.add(changeText)
                        Log.d(TAG, "添加可见变化: $changeText")
                    }
                    "cabin_integrity" -> {
                        // 房屋状况用文字描述
                        when {
                            diff > 0 -> hiddenEffects.add("小屋变得更加坚固了")
                            diff < 0 -> hiddenEffects.add("小屋受到了一些损坏")
                        }
                    }
                    "hope" -> {
                        // 希望值用文字描述
                        when {
                            diff > 10 -> hiddenEffects.add("你感到信心大增")
                            diff > 0 -> hiddenEffects.add("你感到稍微安心了一些")
                            diff < -10 -> hiddenEffects.add("你感到更加绝望了")
                            diff < 0 -> hiddenEffects.add("你感到有些沮丧")
                        }
                    }
                }
            }
        }

        val result = mutableListOf<String>()

        if (visibleChanges.isNotEmpty()) {
            val visibleText = "📈 状态变化：${visibleChanges.joinToString("，")}"
            result.add(visibleText)
            Log.d(TAG, "添加可见变化文本: $visibleText")
        } else {
            Log.d(TAG, "没有可见变化")
        }

        if (hiddenEffects.isNotEmpty()) {
            val hiddenText = "💭 ${hiddenEffects.joinToString("，")}"
            result.add(hiddenText)
            Log.d(TAG, "添加隐藏效果文本: $hiddenText")
        } else {
            Log.d(TAG, "没有隐藏效果")
        }

        val finalText = result.joinToString("\n")
        Log.d(TAG, "最终生成的状态变化文本: '$finalText'")
        return finalText
    }

    /**
     * 处理下一步逻辑
     * 包括加载下一个事件、执行夜晚阶段和检查游戏结束
     */
    private fun processNextStep(choice: Choice) {
        Log.d(TAG, "=== 处理下一步逻辑 ===")
        Log.d(TAG, "选择: ${choice.text}")
        Log.d(TAG, "nextEventId: ${choice.nextEventId}")
        Log.d(TAG, "当前状态: $gameUIState")



        // 清除待处理的选择
        pendingChoice = null

        // 使用选择的下一个事件ID
        val nextEventId = choice.nextEventId

        // c. 如果有 nextEventId，就调用 displayEvent() 来加载下一个事件
        nextEventId?.let { eventId ->
            Log.d(TAG, "准备跳转到下一个事件: $eventId")

            // 检查是否是夜晚事件，如果是则先进入食物阶段
            if (isNightEvent(eventId)) {
                Log.d(TAG, "检测到夜晚事件，先检查是否需要食物阶段")
                if (shouldEnterFoodPhase()) {
                    Log.d(TAG, "进入食物阶段")
                    // 保存夜晚事件ID，食物阶段结束后使用
                    pendingNightEventId = eventId
                    // 强制转换到食物阶段
                    gameUIState = GameUIState.FOOD_CHOICE
                    showFoodChoicePhase()
                } else {
                    Log.d(TAG, "跳过食物阶段，直接进入夜晚事件")
                    if (eventMap.containsKey(eventId)) {
                        displayEvent(eventId)
                    } else {
                        // 如果夜晚事件不存在，执行夜晚阶段
                        Log.d(TAG, "夜晚事件不存在，执行夜晚阶段")
                        if (gameUIState != GameUIState.WAITING_CONTINUE) {
                            Log.d(TAG, "调整状态从 $gameUIState 到 WAITING_CONTINUE")
                            gameUIState = GameUIState.WAITING_CONTINUE
                        }

                        if (transitionState("night_phase")) {
                            performNightPhaseAndCheck()
                        } else {
                            Log.e(TAG, "状态转换失败，强制执行夜晚阶段")
                            forceNightPhase()
                        }
                    }
                }
            } else {
                // 非夜晚事件，正常处理
                if (eventMap.containsKey(eventId)) {
                    Log.d(TAG, "显示事件: $eventId")
                    displayEvent(eventId)
                } else {
                    Log.w(TAG, "事件 $eventId 不存在")
                    proceedToNextDay()
                }
            }
        } ?: run {
            Log.d(TAG, "选择没有下一个事件，检查是否需要食物阶段")
            // 检查是否需要进入食物阶段
            if (shouldEnterFoodPhase()) {
                Log.d(TAG, "进入食物阶段")
                // 强制转换到食物阶段
                gameUIState = GameUIState.FOOD_CHOICE
                showFoodChoicePhase()
            } else {
                Log.d(TAG, "直接执行夜晚阶段")
                // 确保状态正确，然后执行夜晚阶段
                if (gameUIState != GameUIState.WAITING_CONTINUE) {
                    Log.d(TAG, "调整状态从 $gameUIState 到 WAITING_CONTINUE")
                    gameUIState = GameUIState.WAITING_CONTINUE
                }

                if (transitionState("night_phase")) {
                    performNightPhaseAndCheck()
                } else {
                    Log.e(TAG, "状态转换失败，强制执行夜晚阶段")
                    forceNightPhase()
                }
            }
        }

        Log.d(TAG, "=== 下一步逻辑处理完成 ===")
    }

    /**
     * 检查是否应该进入食物阶段
     * 每天夜晚前都会询问玩家是否进食（只要有食物）
     */
    private fun shouldEnterFoodPhase(): Boolean {
        val gameState = GameManager.gameState

        // 只要有食物就进入食物阶段，让玩家选择是否进食
        val hasFood = gameState.food > 0

        Log.d(TAG, "食物阶段检查 - 当前食物:${gameState.food}, 是否进入食物阶段:$hasFood")
        return hasFood
    }

    /**
     * 显示食物选择阶段
     */
    private fun showFoodChoicePhase() {
        Log.d(TAG, "显示食物选择阶段")

        val gameState = GameManager.gameState
        val foodText = "夜晚来临前\n\n夜幕即将降临，你检查了一下自己的食物储备，还有${gameState.food}份食物。\n\n进食可以恢复体力，帮助你度过寒冷的夜晚。你要进食吗？"

        binding.storyTextView.text = foodText

        // 创建食物选择
        val foodChoices = listOf(
            Choice(
                text = "是，进食",
                effects = mapOf("food" to -1, "stamina" to 30, "hope" to 5),
                resultText = "你打开了一份食物，虽然味道一般，但温热的食物让你感到了一丝慰藉。体力得到了恢复。"
            ),
            Choice(
                text = "否，保存食物",
                effects = emptyMap(),
                resultText = "你决定保存食物，为以后做准备。"
            )
        )

        currentChoices = foodChoices
        setupChoiceButtons(currentChoices)

        Log.d(TAG, "食物选择阶段设置完成")
    }

    /**
     * 执行夜晚阶段并检查游戏结束
     */
    private fun performNightPhaseAndCheck() {
        Log.d(TAG, "执行夜晚阶段...")

        // 执行夜晚阶段
        val nightResult = GameManager.performNightPhase()

        // 更新UI显示
        updateUI()

        // 显示夜晚阶段结果，并转换到等待继续状态
        showNightPhaseResult(nightResult)
    }

    /**
     * 强制执行夜晚阶段
     * 确保在任何状态下都能正确触发夜晚结算
     */
    private fun forceNightPhase() {
        Log.d(TAG, "强制执行夜晚阶段，当前状态: $gameUIState")

        // 直接设置为夜晚阶段状态
        gameUIState = GameUIState.NIGHT_PHASE

        // 执行夜晚阶段
        performNightPhaseAndCheck()
    }

    /**
     * 判断是否为夜晚事件
     * 包括所有需要触发夜晚结算的事件
     */
    private fun isNightEvent(eventId: String): Boolean {
        val nightEventPatterns = listOf(
            "night",           // 包含night的事件
            "day1_night",      // 第一天夜晚
            "day2_night",      // 第二天夜晚
            "day3_night",      // 第三天夜晚
            "day4_night",      // 第四天夜晚
            "day5_night",      // 第五天夜晚
            "day6_night",      // 第六天夜晚
            "day7_night"       // 第七天夜晚
        )

        return nightEventPatterns.any { pattern -> eventId.contains(pattern) }
    }

    /**
     * 进入下一天
     */
    private fun proceedToNextDay() {
        val currentDay = GameManager.gameState.currentDay
        val nextEventId = "day${currentDay}_start"

        Log.d(TAG, "进入第 $currentDay 天，事件ID: $nextEventId，当前状态: $gameUIState")

        // 确保状态为正常选择状态
        if (gameUIState != GameUIState.NORMAL_CHOICE) {
            gameUIState = GameUIState.NORMAL_CHOICE
            Log.d(TAG, "进入新一天，重置状态为正常选择")
        }

        // 尝试显示下一天的事件
        if (eventMap.containsKey(nextEventId)) {
            displayEvent(nextEventId)
        } else {
            // 如果没有对应的事件，显示通用的新一天开始文本
            val genericText = "第 $currentDay 天\n\n新的一天开始了。你必须继续为生存而努力。"
            binding.storyTextView.text = genericText

            // 显示通用选择
            val genericChoices = listOf(
                Choice(
                    text = "外出寻找资源",
                    effects = mapOf("stamina" to -15, "firewood" to 10, "warmth" to -10)
                ),
                Choice(
                    text = "在屋内休息",
                    effects = mapOf("stamina" to 15)
                )
            )

            currentChoices = genericChoices
            setupChoiceButtons(currentChoices)
        }
    }

    /**
     * 显示夜晚阶段结果
     */
    private fun showNightPhaseResult(nightResult: GameManager.NightPhaseResult) {
        // 生成夜晚状态变化文本
        val stateChanges = mutableMapOf<String, Pair<Int, Int>>()

        // 添加木柴变化
        if (nightResult.firewoodUsed > 0) {
            stateChanges["firewood"] = Pair(nightResult.firewoodBefore, nightResult.firewoodAfter)
        }

        // 添加体温变化
        if (nightResult.warmthChange != 0) {
            stateChanges["warmth"] = Pair(nightResult.warmthBefore, nightResult.warmthAfter)
        }

        val stateChangesText = generateStateChangesText(stateChanges)

        val nightText = if (nightResult.hadEnoughFirewood) {
            "夜晚降临\n\n壁炉中的火焰温暖地燃烧着，消耗了${nightResult.firewoodUsed}根木柴。你感到体温回升了${nightResult.warmthChange}度。\n\n第${nightResult.dayAfter}天即将到来..."
        } else {
            "夜晚降临\n\n没有足够的木柴生火，寒冷侵袭着你的身体。你的体温下降了${-nightResult.warmthChange}度。\n\n第${nightResult.dayAfter}天即将到来..."
        }

        // 组合夜晚文本和状态变化文本
        val fullNightText = if (stateChangesText.isNotEmpty()) {
            "$nightText\n\n$stateChangesText"
        } else {
            nightText
        }

        binding.storyTextView.text = fullNightText

        // 直接转换到夜晚等待继续状态，显示"继续下一天"按钮
        if (transitionState("night_shown")) {
            setupChoiceButtons(emptyList()) // 显示继续按钮
        } else {
            // 如果转换失败，强制设置状态
            Log.w(TAG, "夜晚状态转换失败，强制设置为等待继续状态")
            gameUIState = GameUIState.NIGHT_WAITING_CONTINUE
            setupChoiceButtons(emptyList())
        }

        Log.d(TAG, "显示夜晚阶段结果: ${nightText.take(50)}...")
    }

    /**
     * 生成夜晚结算文本
     */
    private fun generateNightSettlementText(nightResult: GameManager.NightPhaseResult): String {
        val firewoodText = if (nightResult.hadEnoughFirewood) {
            "壁炉中的火焰温暖地燃烧着，消耗了${nightResult.firewoodUsed}根木柴。"
        } else {
            "没有足够的木柴生火，寒冷侵袭着你的身体。"
        }

        val warmthText = if (nightResult.warmthChange > 0) {
            "你感到体温回升了${nightResult.warmthChange}度。"
        } else {
            "你的体温下降了${-nightResult.warmthChange}度。"
        }

        val staminaText = "经过一夜的煎熬，你的体力下降了${-nightResult.staminaChange}点。"

        val roofLeakingText = if (nightResult.roofLeaking) {
            "屋顶的漏洞让冰冷的雪水滴在你身上，额外消耗了${nightResult.roofLeakingWarmthLoss}点体温。"
        } else {
            ""
        }

        val baseText = if (roofLeakingText.isNotEmpty()) {
            "$firewoodText $warmthText $staminaText $roofLeakingText"
        } else {
            "$firewoodText $warmthText $staminaText"
        }

        // 生成状态变化文本
        val stateChanges = mutableMapOf<String, Pair<Int, Int>>()

        // 添加木柴变化
        if (nightResult.firewoodUsed > 0) {
            stateChanges["firewood"] = Pair(nightResult.firewoodBefore, nightResult.firewoodAfter)
        }

        // 添加体温变化
        if (nightResult.warmthChange != 0) {
            stateChanges["warmth"] = Pair(nightResult.warmthBefore, nightResult.warmthAfter)
        }

        // 添加体力变化
        if (nightResult.staminaChange != 0) {
            stateChanges["stamina"] = Pair(nightResult.staminaBefore, nightResult.staminaAfter)
        }

        val stateChangesText = generateStateChangesText(stateChanges)

        // 添加隐藏数值影响的说明
        val gameState = GameManager.gameState
        val hiddenEffectsText = mutableListOf<String>()

        // 房屋状况影响
        when {
            gameState.cabinIntegrity >= 70 -> hiddenEffectsText.add("房屋状况良好，额外保温效果")
            gameState.cabinIntegrity <= 20 -> hiddenEffectsText.add("房屋破损严重，保温效果差")
        }

        // 希望值影响
        when {
            gameState.hope >= 80 -> hiddenEffectsText.add("高昂的斗志帮助你恢复体力")
            gameState.hope <= 20 -> hiddenEffectsText.add("绝望的情绪消耗了你的体力")
        }

        val fullText = mutableListOf<String>()
        fullText.add("夜晚结算\n\n$baseText")

        if (stateChangesText.isNotEmpty()) {
            fullText.add(stateChangesText)
        }

        if (hiddenEffectsText.isNotEmpty()) {
            fullText.add("💭 环境影响：${hiddenEffectsText.joinToString("；")}")
        }

        return fullText.joinToString("\n\n")
    }

    /**
     * 显示游戏结束对话框
     * 根据 warmth 和 currentDay 的值显示"你胜利了"或"你失败了"，并提供一个"重新开始"的按钮
     */
    private fun showGameOverDialog() {
        val gameState = GameManager.gameState
        val isVictory = GameManager.isVictory()
        val reason = GameManager.getGameOverReason()

        Log.d(TAG, "显示游戏结束对话框: 胜利=$isVictory, 原因=$reason")

        if (isVictory) {
            // 胜利时显示特殊的胜利页面
            showVictoryScreen()
        } else {
            // 失败时保存当前状态并显示特殊的失败页面
            saveGameStateForRevive()
            showFailureScreen(reason)
        }
    }

    /**
     * 自动备份当前有效的游戏状态
     * 在每次状态变化后调用，确保我们总是有一个有效的备份状态
     */
    private fun backupValidGameState() {
        val currentState = GameManager.gameState

        // 只有当前状态是有效的（角色还活着）时才备份
        if (currentState.warmth > 0 && currentState.stamina > 0 && currentState.hope > 0) {
            lastValidGameState = currentState.copy()
            lastValidCurrentEvent = currentEvent
            lastValidCurrentChoices = currentChoices.toList()
            lastValidGameUIState = gameUIState

            Log.d(TAG, "备份有效游戏状态: 体温=${currentState.warmth}, 体力=${currentState.stamina}, 希望=${currentState.hope}")
        }
    }

    /**
     * 保存游戏状态用于广告复活
     */
    private fun saveGameStateForRevive() {
        Log.d(TAG, "保存游戏状态用于广告复活")

        // 优先使用最后一个有效的备份状态，如果没有则使用当前状态并修正
        val stateToSave = lastValidGameState ?: GameManager.gameState
        val eventToSave = lastValidCurrentEvent ?: currentEvent
        val choicesToSave = if (lastValidCurrentChoices.isNotEmpty()) lastValidCurrentChoices else currentChoices
        val uiStateToSave = lastValidGameUIState

        // 如果使用的是当前状态且角色已死亡，则修正数值
        val finalState = if (lastValidGameState != null) {
            // 使用备份的有效状态
            stateToSave
        } else {
            // 修正当前死亡状态的数值
            val currentState = GameManager.gameState
            val revivedWarmth = if (currentState.warmth <= 0) 30 else currentState.warmth
            val revivedStamina = if (currentState.stamina <= 0) 30 else currentState.stamina
            val revivedHope = if (currentState.hope <= 0) 20 else currentState.hope

            GameState(
                warmth = revivedWarmth,
                stamina = revivedStamina,
                firewood = currentState.firewood,
                food = currentState.food,
                currentDay = currentState.currentDay,
                cabinIntegrity = currentState.cabinIntegrity,
                hope = revivedHope,
                specialItems = currentState.specialItems.toMutableSet(),
                roofLeaking = currentState.roofLeaking,
                specialStates = currentState.specialStates.toMutableMap()
            )
        }

        savedGameState = finalState
        savedCurrentEvent = eventToSave
        savedCurrentChoices = choicesToSave.toList()
        savedGameUIState = when (uiStateToSave) {
            GameUIState.GAME_OVER -> GameUIState.NORMAL_CHOICE
            else -> uiStateToSave
        }

        Log.d(TAG, "游戏状态已保存用于复活:")
        Log.d(TAG, "  使用备份状态: ${lastValidGameState != null}")
        Log.d(TAG, "  复活状态: 体温=${finalState.warmth}, 体力=${finalState.stamina}, 希望=${finalState.hope}")
        Log.d(TAG, "  其他数值: 木柴=${finalState.firewood}, 食物=${finalState.food}, 房屋完整度=${finalState.cabinIntegrity}")
        Log.d(TAG, "  事件=${savedCurrentEvent?.id}, 选择数=${savedCurrentChoices.size}, UI状态=${savedGameUIState}")
    }

    /**
     * 显示特殊的胜利页面
     */
    private fun showVictoryScreen() {
        val gameState = GameManager.gameState

        // 隐藏所有按钮
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE
        binding.choice4Button.visibility = View.GONE

        // 创建胜利文本
        val hasSignalGun = gameState.specialItems.contains("信号枪")

        val victoryType = GameManager.getGameOverReason()
        val victoryText = when (victoryType) {
            "完美救援" -> {
                """
                    恭喜！你获得了完美结局！

                    雪山求生 - 完美救援结局

                    经过7天的艰苦求生，你以最完美的状态迎来了胜利！

                    你向天空发射了信号枪，红色的信号弹在雪白的天空中格外醒目。你的坚强意志和不屈精神感动了所有人。

                    第8天清晨，风雪停了。一缕久违的阳光照在你的脸上，温暖如春。远处传来了救援直升机的轰鸣声。

                    "我们收到了你的信号！"救援队长激动地说，"你的求生意志令人敬佩！"

                    你不仅活下来了，还保持了高昂的斗志！这是最完美的结局！

                    === 完美救援成就 ===
                    存活天数: 7天 | 最终体温: ${gameState.warmth} | 最终体力: ${gameState.stamina}
                    剩余木柴: ${gameState.firewood} | 剩余食物: ${gameState.food}
                    特殊物品: ${gameState.specialItems.size}个

                    🏆 你是真正的雪山求生大师！获得了传奇级成就！
                """.trimIndent()
            }
            "坚强意志" -> {
                """
                    恭喜！你展现了坚强的意志！

                    雪山求生 - 坚强意志结局

                    经过7天的艰苦求生，你凭借坚强的意志力战胜了绝境！

                    第8天清晨，风雪停了。虽然救援还没有到来，但你的内心充满了希望和力量。

                    你相信自己能够继续坚持下去，直到获救的那一天。你的坚强意志是你最大的财富。

                    === 坚强意志成就 ===
                    存活天数: 7天 | 最终体温: ${gameState.warmth} | 最终体力: ${gameState.stamina}
                    剩余木柴: ${gameState.firewood} | 剩余食物: ${gameState.food}

                    💪 你的意志力令人敬佩！
                """.trimIndent()
            }
            "成功存活" -> {
                """
                    恭喜！你成功存活了！

                    雪山求生 - 生存结局

                    经过7天的艰苦求生，你终于迎来了胜利的曙光！

                    第8天清晨，风雪停了。你走出小屋，深深地呼吸着清新的空气。

                    虽然过程艰难，但你凭借智慧和勇气成功存活了下来。

                    === 生存成就 ===
                    存活天数: 7天 | 最终体温: ${gameState.warmth} | 最终体力: ${gameState.stamina}
                    剩余木柴: ${gameState.firewood} | 剩余食物: ${gameState.food}

                    ✅ 你是合格的求生者！
                """.trimIndent()
            }
            "勉强存活" -> {
                """
                    你勉强存活了下来...

                    雪山求生 - 勉强存活结局

                    经过7天的痛苦挣扎，你终于看到了第8天的曙光...

                    虽然身心俱疲，希望渺茫，但你还是坚持到了最后。

                    你的身体和精神都已经到了极限，但至少你还活着。

                    === 勉强存活 ===
                    存活天数: 7天 | 最终体温: ${gameState.warmth} | 最终体力: ${gameState.stamina}
                    剩余木柴: ${gameState.firewood} | 剩余食物: ${gameState.food}

                    😔 虽然艰难，但你还是活下来了...
                """.trimIndent()
            }
            else -> {
                // 默认生存结局
                """
                    恭喜！你成功存活了！

                    雪山求生 - 生存结局

                    经过7天的艰苦求生，你终于迎来了胜利的曙光！

                    === 生存成就 ===
                    存活天数: 7天 | 最终体温: ${gameState.warmth} | 最终体力: ${gameState.stamina}
                    剩余木柴: ${gameState.firewood} | 剩余食物: ${gameState.food}
                """.trimIndent()
            }
        }

        // 设置胜利文本
        binding.storyTextView.text = victoryText

        // 转换到游戏结束状态
        gameUIState = GameUIState.GAME_OVER

        // 立即显示选择按钮
        showVictoryButtons()
    }

    /**
     * 显示胜利页面的按钮
     */
    private fun showVictoryButtons() {
        binding.choice1Button.visibility = View.VISIBLE
        binding.choice1Button.text = "再次挑战"

        binding.choice2Button.visibility = View.VISIBLE
        binding.choice2Button.text = "离开雪山"

        // 设置按钮点击事件
        binding.choice1Button.setOnClickListener {
            restartGame()
        }

        binding.choice2Button.setOnClickListener {
            MusicManager.stopBackgroundMusic() // 停止音乐
            finish() // 关闭应用
        }
    }

    /**
     * 显示特殊的失败页面
     */
    private fun showFailureScreen(reason: String) {
        val gameState = GameManager.gameState

        // 隐藏所有按钮
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE
        binding.choice4Button.visibility = View.GONE

        // 创建失败文本
        val failureText = when (reason) {
            "体温过低" -> """
                游戏结束

                雪山求生 - 冻死结局

                很遗憾，你没能在严寒中坚持下去...

                你的意识渐渐模糊，壁炉的火焰在你眼中变成了遥远的星辰。寒冷如潮水般涌来，吞噬了你最后的温暖。

                你的身体渐渐失去知觉，呼吸变得越来越微弱。在生命的最后时刻，你想起了家人的温暖怀抱...

                你再也没有醒来。

                === 死亡统计 ===
                死亡原因: 体温过低
                存活天数: ${gameState.currentDay}天
                最终体温: ${gameState.warmth}
                最终体力: ${gameState.stamina}
                剩余木柴: ${gameState.firewood}
                剩余食物: ${gameState.food}

                雪山吞噬了又一个生命...
            """.trimIndent()
            "体力耗尽" -> """
                游戏结束

                雪山求生 - 力竭结局

                很遗憾，你的身体已经无法承受更多的折磨...

                连日的求生让你精疲力竭，每一次呼吸都变得困难。你的双腿再也无法支撑你的身体，颤抖的双手再也无法握紧任何东西。

                你缓缓倒在雪地上，望着灰暗的天空。体力的完全耗尽让你无法再继续战斗下去。

                你缓缓闭上了眼睛，永远地沉睡在这片雪山中。

                === 死亡统计 ===
                死亡原因: 体力耗尽
                存活天数: ${gameState.currentDay}天
                最终体温: ${gameState.warmth}
                最终体力: ${gameState.stamina}
                剩余木柴: ${gameState.firewood}
                剩余食物: ${gameState.food}

                雪山又夺走了一个勇敢的灵魂...
            """.trimIndent()
            "绝望而死" -> """
                游戏结束

                雪山求生 - 绝望结局

                很遗憾，你的内心被绝望完全吞噬了...

                连日的挣扎让你失去了所有的希望。你坐在小屋的角落里，眼神空洞地望着窗外的风雪。

                "我再也坚持不下去了..."你喃喃自语道。

                绝望如黑暗一般笼罩着你的心灵，你放弃了所有的努力，放弃了生存的意志。

                在这片冰冷的雪山中，你的心先于身体死去了。

                === 死亡统计 ===
                死亡原因: 绝望而死
                存活天数: ${gameState.currentDay}天
                最终体温: ${gameState.warmth}
                最终体力: ${gameState.stamina}
                剩余木柴: ${gameState.firewood}
                剩余食物: ${gameState.food}

                有时候，心死比身死更可怕...
            """.trimIndent()
            else -> """
                游戏结束

                雪山求生 - 死亡结局

                $reason

                === 死亡统计 ===
                存活天数: ${gameState.currentDay}天
                最终体温: ${gameState.warmth}
                最终体力: ${gameState.stamina}
                剩余木柴: ${gameState.firewood}
                剩余食物: ${gameState.food}

                愿逝者安息...
            """.trimIndent()
        }

        // 设置失败文本
        binding.storyTextView.text = failureText

        // 转换到游戏结束状态
        gameUIState = GameUIState.GAME_OVER

        // 立即显示选择按钮
        showFailureButtons()
    }

    /**
     * 显示失败页面的按钮
     */
    private fun showFailureButtons() {
        binding.choice1Button.visibility = View.VISIBLE
        binding.choice1Button.text = "广告复活"

        binding.choice2Button.visibility = View.VISIBLE
        binding.choice2Button.text = "重新挑战"

        binding.choice3Button.visibility = View.VISIBLE
        binding.choice3Button.text = "离开雪山"

        // 设置按钮点击事件
        binding.choice1Button.setOnClickListener {
            showReviveAd()
        }

        binding.choice2Button.setOnClickListener {
            restartGame()
        }

        binding.choice3Button.setOnClickListener {
            MusicManager.stopBackgroundMusic() // 停止音乐
            finish() // 关闭应用
        }
    }

    /**
     * 重新开始游戏
     */
    private fun restartGame() {
        Log.d(TAG, "重新开始游戏")

        // 重置 GameManager
        GameManager.resetGame()

        // 重置UI状态
        gameUIState = GameUIState.NORMAL_CHOICE
        pendingChoice = null
        currentChoices = emptyList()
        pendingNightEventId = null

        // 重新设置按钮点击监听器
        setupClickListeners()

        // 隐藏所有按钮，准备重新开始
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE
        binding.choice4Button.visibility = View.GONE

        // 重新开始第一个事件
        displayEvent("day1_start")
    }



    /**
     * 评估条件事件
     * 根据游戏状态条件选择对应的事件
     * @param conditionalEvents 条件事件映射
     * @return 选中的事件ID，如果没有匹配的条件则返回null
     */
    private fun evaluateConditionalEvent(conditionalEvents: Map<String, String>): String? {
        val gameState = GameManager.gameState

        Log.d(TAG, "评估条件事件，当前状态:")
        Log.d(TAG, "  房屋状况: ${gameState.cabinIntegrity}")
        Log.d(TAG, "  希望值: ${gameState.hope}")
        Log.d(TAG, "  体温: ${gameState.warmth}")
        Log.d(TAG, "  体力: ${gameState.stamina}")

        // 评估每个条件
        conditionalEvents.forEach { (condition, eventId) ->
            if (condition == "default") {
                // 跳过默认条件，最后处理
                return@forEach
            }

            Log.d(TAG, "检查条件: $condition -> $eventId")

            if (evaluateCondition(condition, gameState)) {
                Log.d(TAG, "条件满足: $condition")
                return eventId
            }
        }

        // 如果没有条件满足，返回默认事件
        val defaultEventId = conditionalEvents["default"]
        if (defaultEventId != null) {
            Log.d(TAG, "使用默认事件: $defaultEventId")
        }

        return defaultEventId
    }

    /**
     * 评估单个条件
     * @param condition 条件字符串，如 "cabin_integrity < 50"
     * @param gameState 游戏状态
     * @return 条件是否满足
     */
    private fun evaluateCondition(condition: String, gameState: GameState): Boolean {
        try {
            // 特殊处理：对于特殊状态的简单布尔检查
            val specialStates = listOf("hasTools", "hasTrap", "hasBackupShelter", "knowsBackupShelter",
                                     "inBackupShelter", "hasSurvivalKnowledge", "hasRescueDevice", "signalFired")
            if (specialStates.contains(condition.trim())) {
                val result = gameState.hasSpecialState(condition.trim())
                Log.d(TAG, "特殊状态检查: $condition = $result")
                if (condition.trim() == "signalFired") {
                    Log.d(TAG, "信号枪状态详细检查: signalFired = $result")
                    Log.d(TAG, "所有特殊状态: ${gameState.specialStates}")
                }
                return result
            }

            // 特殊处理：检查信号枪
            if (condition.trim() == "hasSignalGun") {
                val result = gameState.specialItems.contains("signal_gun")
                Log.d(TAG, "信号枪检查: $condition = $result")
                return result
            }

            // 解析条件字符串
            val parts = condition.trim().split(" ")
            if (parts.size != 3) {
                Log.w(TAG, "无效的条件格式: $condition")
                return false
            }

            val property = parts[0]
            val operator = parts[1]
            val value = parts[2].toIntOrNull() ?: return false

            val currentValue = when (property) {
                "cabin_integrity" -> gameState.cabinIntegrity
                "hope" -> gameState.hope
                "warmth" -> gameState.warmth
                "stamina" -> gameState.stamina
                "firewood" -> gameState.firewood
                "food" -> gameState.food
                "currentDay" -> gameState.currentDay
                // 特殊状态检查
                "hasTools", "hasTrap", "hasBackupShelter", "knowsBackupShelter",
                "inBackupShelter", "hasSurvivalKnowledge" -> {
                    if (gameState.hasSpecialState(property)) 1 else 0
                }
                else -> {
                    Log.w(TAG, "未知的属性: $property")
                    return false
                }
            }

            val result = when (operator) {
                "<" -> currentValue < value
                "<=" -> currentValue <= value
                ">" -> currentValue > value
                ">=" -> currentValue >= value
                "==" -> currentValue == value
                "!=" -> currentValue != value
                else -> {
                    Log.w(TAG, "未知的操作符: $operator")
                    false
                }
            }

            Log.d(TAG, "条件评估: $property($currentValue) $operator $value = $result")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "条件评估错误: $condition", e)
            return false
        }
    }

    /**
     * 显示激励广告获取资源
     * @param resourceType 资源类型
     */
    private fun showAdForResource(resourceType: AdManager.ResourceType) {
        Log.d(TAG, "用户点击获取${resourceType.displayName}的广告按钮")

        // 显示加载提示
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("加载广告")
            .setMessage("正在加载广告，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 调用广告管理器显示广告
        AdManager.showRewardVideoForResource(this, resourceType, object : AdManager.AdCallback {
            override fun onAdLoadSuccess() {
                Log.d(TAG, "广告加载成功")
                loadingDialog.dismiss()
            }

            override fun onAdLoadFailed(error: String) {
                Log.e(TAG, "广告加载失败: $error")
                loadingDialog.dismiss()
                showAdErrorDialog("广告加载失败", "无法加载广告，请稍后再试。\n错误信息: $error")
            }

            override fun onAdShowSuccess() {
                Log.d(TAG, "广告开始播放")
            }

            override fun onAdShowFailed(error: String) {
                Log.e(TAG, "广告播放失败: $error")
                showAdErrorDialog("广告播放失败", "广告播放出现问题，请稍后再试。\n错误信息: $error")
            }

            override fun onAdRewarded(rewardedResourceType: AdManager.ResourceType) {
                Log.d(TAG, "广告观看完成，获得奖励: ${rewardedResourceType.displayName} +${rewardedResourceType.rewardAmount}")

                // 给予玩家奖励
                giveResourceReward(rewardedResourceType)

                // 显示奖励提示
                showRewardDialog(rewardedResourceType)
            }

            override fun onAdClosed() {
                Log.d(TAG, "广告关闭")
            }
        })
    }

    /**
     * 给予玩家资源奖励
     * @param resourceType 资源类型
     */
    private fun giveResourceReward(resourceType: AdManager.ResourceType) {
        val gameState = GameManager.gameState

        when (resourceType) {
            AdManager.ResourceType.WARMTH -> {
                gameState.warmth = (gameState.warmth + resourceType.rewardAmount).coerceAtMost(100)
                Log.d(TAG, "体温增加${resourceType.rewardAmount}，当前体温: ${gameState.warmth}")
            }
            AdManager.ResourceType.STAMINA -> {
                gameState.stamina = (gameState.stamina + resourceType.rewardAmount).coerceAtMost(100)
                Log.d(TAG, "体力增加${resourceType.rewardAmount}，当前体力: ${gameState.stamina}")
            }
            AdManager.ResourceType.FIREWOOD -> {
                gameState.firewood = (gameState.firewood + resourceType.rewardAmount).coerceAtMost(50)
                Log.d(TAG, "木柴增加${resourceType.rewardAmount}，当前木柴: ${gameState.firewood}")
            }
            AdManager.ResourceType.FOOD -> {
                gameState.food = (gameState.food + resourceType.rewardAmount).coerceAtMost(20)
                Log.d(TAG, "食物增加${resourceType.rewardAmount}，当前食物: ${gameState.food}")
            }
        }

        // 更新UI显示
        updateUI()
    }

    /**
     * 显示奖励对话框
     * @param resourceType 资源类型
     */
    private fun showRewardDialog(resourceType: AdManager.ResourceType) {
        AlertDialog.Builder(this)
            .setTitle("获得奖励！")
            .setMessage("恭喜！您获得了 ${resourceType.displayName} +${resourceType.rewardAmount}")
            .setPositiveButton("确定") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示广告错误对话框
     * @param title 标题
     * @param message 消息
     */
    private fun showAdErrorDialog(title: String, message: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("确定") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示复活广告
     */
    private fun showReviveAd() {
        Log.d(TAG, "用户点击广告复活按钮")

        // 显示加载提示
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("加载复活广告")
            .setMessage("正在加载复活广告，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 调用广告管理器显示复活广告
        AdManager.showReviveAd(this, object : AdManager.ReviveAdCallback {
            override fun onAdLoadSuccess() {
                Log.d(TAG, "复活广告加载成功")
                loadingDialog.dismiss()
            }

            override fun onAdLoadFailed(error: String) {
                Log.e(TAG, "复活广告加载失败: $error")
                loadingDialog.dismiss()
                showAdErrorDialog("复活广告加载失败", "无法加载复活广告，请稍后再试。\n错误信息: $error")
            }

            override fun onAdShowSuccess() {
                Log.d(TAG, "复活广告开始播放")
            }

            override fun onAdShowFailed(error: String) {
                Log.e(TAG, "复活广告播放失败: $error")
                showAdErrorDialog("复活广告播放失败", "复活广告播放出现问题，请稍后再试。\n错误信息: $error")
            }

            override fun onReviveSuccess() {
                Log.d(TAG, "复活广告观看完成，开始复活角色")

                // 复活角色
                reviveCharacter()

                // 显示复活成功提示
                showReviveSuccessDialog()
            }

            override fun onAdClosed() {
                Log.d(TAG, "复活广告关闭")
            }
        })
    }

    /**
     * 复活角色
     */
    private fun reviveCharacter() {
        Log.d(TAG, "开始复活角色")

        savedGameState?.let { savedState ->
            Log.d(TAG, "恢复保存的游戏状态:")
            Log.d(TAG, "  体温: ${savedState.warmth}")
            Log.d(TAG, "  体力: ${savedState.stamina}")
            Log.d(TAG, "  木柴: ${savedState.firewood}")
            Log.d(TAG, "  食物: ${savedState.food}")
            Log.d(TAG, "  希望: ${savedState.hope}")
            Log.d(TAG, "  房屋完整度: ${savedState.cabinIntegrity}")
            Log.d(TAG, "  当前天数: ${savedState.currentDay}")

            // 直接恢复游戏状态
            GameManager.setGameState(savedState)

            // 验证状态是否正确设置
            val currentGameState = GameManager.gameState
            Log.d(TAG, "验证恢复后的游戏状态:")
            Log.d(TAG, "  体温: ${currentGameState.warmth}")
            Log.d(TAG, "  体力: ${currentGameState.stamina}")
            Log.d(TAG, "  木柴: ${currentGameState.firewood}")
            Log.d(TAG, "  食物: ${currentGameState.food}")
            Log.d(TAG, "  希望: ${currentGameState.hope}")
            Log.d(TAG, "  房屋完整度: ${currentGameState.cabinIntegrity}")

            // 恢复UI状态
            currentEvent = savedCurrentEvent
            currentChoices = savedCurrentChoices
            gameUIState = savedGameUIState

            // 重新设置按钮点击监听器（重要：确保按钮可以正常点击）
            setupClickListeners()

            // 更新UI显示
            updateUI()

            // 重新显示当前事件
            savedCurrentEvent?.let { event ->
                Log.d(TAG, "恢复保存的事件: ${event.id}")
                binding.storyTextView.text = event.text
                setupChoiceButtons(savedCurrentChoices)
            } ?: run {
                // 如果没有保存的事件，尝试恢复到合适的状态
                Log.w(TAG, "没有保存的事件，尝试恢复到当前天的开始")
                val currentDay = savedState.currentDay
                val dayStartEventId = "day${currentDay}_start"
                if (eventMap.containsKey(dayStartEventId)) {
                    displayEvent(dayStartEventId)
                } else {
                    // 如果没有对应的事件，显示通用状态
                    binding.storyTextView.text = "第 $currentDay 天\n\n你成功复活了！继续你的雪山求生之旅。"

                    // 显示通用选择
                    val genericChoices = listOf(
                        Choice(
                            text = "外出寻找资源",
                            effects = mapOf("stamina" to -15, "firewood" to 10, "warmth" to -10)
                        ),
                        Choice(
                            text = "在屋内休息",
                            effects = mapOf("stamina" to 15)
                        )
                    )

                    currentChoices = genericChoices
                    gameUIState = GameUIState.NORMAL_CHOICE
                    setupChoiceButtons(currentChoices)
                }
            }

            Log.d(TAG, "角色复活完成，最终状态: 体温=${currentGameState.warmth}, 体力=${currentGameState.stamina}, UI状态=${gameUIState}")
        } ?: run {
            Log.e(TAG, "复活失败：没有保存的游戏状态")
        }
    }

    /**
     * 显示复活成功对话框
     */
    private fun showReviveSuccessDialog() {
        AlertDialog.Builder(this)
            .setTitle("复活成功！")
            .setMessage("恭喜！你已经成功复活，可以继续你的雪山求生之旅。")
            .setPositiveButton("继续游戏") { dialog, _ ->
                dialog.dismiss()
                // 确保复活后按钮状态正确
                ensureButtonsAreClickable()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 确保按钮在复活后可以正常点击
     */
    private fun ensureButtonsAreClickable() {
        Log.d(TAG, "确保按钮状态正确，当前UI状态: $gameUIState")

        // 重新设置按钮监听器
        setupClickListeners()

        // 根据当前状态重新设置按钮
        when (gameUIState) {
            GameUIState.NORMAL_CHOICE -> {
                if (currentChoices.isNotEmpty()) {
                    setupChoiceButtons(currentChoices)
                } else {
                    Log.w(TAG, "正常选择状态但没有可用选择，尝试恢复")
                    // 如果没有选择，尝试重新显示当前事件
                    currentEvent?.let { event ->
                        displayEvent(event.id)
                    }
                }
            }
            GameUIState.WAITING_CONTINUE -> {
                setupChoiceButtons(emptyList()) // 显示继续按钮
            }
            else -> {
                Log.d(TAG, "其他UI状态: $gameUIState，保持当前按钮设置")
            }
        }

        Log.d(TAG, "按钮状态检查完成")
    }

    override fun onPause() {
        super.onPause()
        MusicManager.pauseBackgroundMusic()
    }

    override fun onResume() {
        super.onResume()
        // 恢复音乐播放
        MusicManager.resumeBackgroundMusic()
        // 预加载广告
        AdManager.preloadRewardVideo(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        // 注意：不在这里停止音乐，因为可能要返回到开始界面
    }
}